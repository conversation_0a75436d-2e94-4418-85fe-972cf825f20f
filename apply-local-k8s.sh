#!/bin/bash
set -e

# Build the Docker image
echo "Building Docker image..."
docker build -t mneme:latest .

# Check if minikube is running
if command -v minikube &> /dev/null; then
  if minikube status | grep -q "Running"; then
    echo "Loading Docker image into minikube..."
    minikube image load mneme:latest
  fi
fi

# Apply the Kubernetes configuration
echo "Applying Kubernetes configuration..."
kubectl apply -f k8s/overlays/local/namespace.yaml
kubectl apply -k k8s/overlays/local

echo "Kubernetes configuration applied successfully!"
echo "You can now test the job creation with:"
echo "kubectl -n mneme-local create job --from=cronjob/mneme-db-restore-sanitize test-restore-sanitize"
