# Mneme

A Go program that performs scheduled database dumps (PostgreSQL and MySQL) and uploads them to S3.

## Requirements

- Go 1.21 or later
- PostgreSQL client tools (`pg_dump`)
- MySQL client tools (`mysqldump`)
- AWS credentials configured (either through environment variables, AWS CLI config, or IAM role)
- Required AWS permissions:
  - SecretsManager:GetSecretValue
  - S3:PutObject
  - RDS:DescribeDBClusters (for RDS cluster discovery)
  - RDS:DescribeDBInstances (for RDS cluster discovery)

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   go mod download
   ```
3. Build the program:
   ```bash
   go build
   ```

## Configuration

The program expects a JSON configuration stored in AWS Secrets Manager with the following structure:

```json
{
  "databases": [
    {
      "type": "postgresql",
      "host": "db.example.com",
      "port": 5432,
      "database": "mydb",
      "username": "user",
      "password": "password",
      "s3Bucket": "my-backup-bucket",
      "s3Path": "postgresql/daily"
    },
    {
      "type": "mysql",
      "clusterName": "my-rds-cluster",
      "port": 3306,
      "database": "mydb",
      "username": "user",
      "password": "password",
      "s3Bucket": "my-backup-bucket",
      "s3Path": "mysql/daily",
      "enabled": false
    }
  ]
}
```

### RDS Cluster Support

For main backup jobs (not sanitize jobs), the program supports automatic RDS cluster endpoint discovery:

- **`clusterName`**: Specify the RDS cluster name instead of a direct endpoint. The program will automatically discover available instances in the cluster and prefer reader instances for backups, falling back to writer instances if no readers are available.
- **`host`**: Can still be used for direct endpoints or as a fallback if cluster discovery fails.

When using RDS clusters:
1. The program queries AWS RDS APIs to list cluster members
2. Identifies reader vs writer instances
3. Prefers reader instances for backup operations to reduce load on the primary
4. Falls back to writer instances if no readers are available
5. Uses the discovered endpoint and port for the backup operation

For the db-restore-sanitize tool, you can also use the `enabled` flag to temporarily disable specific databases:

```json
{
  "databases": [
    {
      "type": "mysql",
      "host": "mysql.example.com",
      "port": 3306,
      "database": "tracker",
      "username": "root",
      "password": "password",
      "s3Bucket": "my-backup-bucket",
      "s3SourcePath": "mysql/backups",
      "s3DestinationPath": "mysql/sanitized",
      "useLatestBackup": true,
      "sanitizedFilePrefix": "sanitized",
      "enabled": false
    }
  ]
}
```

The `enabled` flag is optional. If not specified, the database is considered enabled by default.

## Usage

Run the program with the AWS Secrets Manager ARN as an argument:

```bash
./mneme -local arn:aws:secretsmanager:region:account:secret:secret-name
```

-local lets you run directly without k8s support

The program will:
1. Fetch the configuration from AWS Secrets Manager
2. Schedule database dumps according to the specified intervals
3. Perform the dumps in parallel
4. Upload the dumps to the specified S3 buckets
5. Clean up temporary files after successful uploads

## Logging

The program logs its activities to stdout, including:
- Start and completion of each dump
- Any errors that occur during the process
- Successful uploads to S3
- Warnings about temporary file cleanup

## Error Handling

The program will:
- Continue running even if one database dump fails
- Log all errors for debugging
- Retry on the next scheduled interval if a dump fails