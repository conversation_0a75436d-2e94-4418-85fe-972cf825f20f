package main

import (
	"time"
)

// Config represents the configuration for all databases
type Config struct {
	Databases []DatabaseConfig `json:"databases"`
}

// DatabaseConfig represents the configuration for a single database
type DatabaseConfig struct {
	Type        string `json:"type"`        // "postgresql" or "mysql"
	Host        string `json:"host"`        // Direct endpoint or RDS cluster name
	ClusterName string `json:"clusterName"` // RDS cluster name (optional, takes precedence over Host for cluster discovery)
	Port        int    `json:"port"`
	Database    string `json:"database"`
	Username    string `json:"username"`
	Password    string `json:"password"`
	S3Bucket    string `json:"s3Bucket"`
	S3Path      string `json:"s3Path"`            // Path within the bucket to store dumps
	Enabled     *bool  `json:"enabled,omitempty"` // Whether this database is enabled for processing (default: true if not specified)
}

// DumpResult represents the result of a database dump operation
type DumpResult struct {
	DatabaseName string
	FilePath     string
	Error        error
	StartTime    time.Time
	EndTime      time.Time
}
