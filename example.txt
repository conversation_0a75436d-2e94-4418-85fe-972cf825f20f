{"databases": [{"type": "postgresql", "host": "db1.example.com", "port": 5432, "database": "db1", "username": "user1", "password": "password1", "s3Bucket": "my-backup-bucket", "s3Path": "hourly/db1"}, {"type": "mysql", "host": "db2.example.com", "port": 3306, "database": "db2", "username": "user2", "password": "password2", "s3Bucket": "my-backup-bucket", "s3Path": "hourly/db2"}, {"type": "postgresql", "host": "db3.example.com", "port": 5432, "database": "db3", "username": "user3", "password": "password3", "s3Bucket": "my-backup-bucket", "s3Path": "hourly/db3"}]}