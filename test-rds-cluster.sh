#!/bin/bash

# Test script for RDS cluster discovery functionality
# This script tests the new RDS cluster discovery feature

echo "Testing RDS cluster discovery functionality..."

# Test 1: Test with a configuration that has clusterName
echo "Test 1: Testing with clusterName configuration"
cat > test-cluster-config.json << EOF
{
  "databases": [
    {
      "type": "postgresql",
      "clusterName": "test-cluster",
      "port": 5432,
      "database": "testdb",
      "username": "testuser",
      "password": "testpass",
      "s3Bucket": "test-bucket",
      "s3Path": "test/path"
    }
  ]
}
EOF

echo "Created test configuration with clusterName"
echo "Configuration:"
cat test-cluster-config.json

# Test 2: Test with a configuration that has direct host
echo -e "\nTest 2: Testing with direct host configuration"
cat > test-direct-config.json << EOF
{
  "databases": [
    {
      "type": "mysql",
      "host": "direct-mysql.example.com",
      "port": 3306,
      "database": "testdb",
      "username": "testuser",
      "password": "testpass",
      "s3Bucket": "test-bucket",
      "s3Path": "test/path"
    }
  ]
}
EOF

echo "Created test configuration with direct host"
echo "Configuration:"
cat test-direct-config.json

# Test 3: Test with mixed configuration
echo -e "\nTest 3: Testing with mixed configuration"
cat > test-mixed-config.json << EOF
{
  "databases": [
    {
      "type": "postgresql",
      "clusterName": "postgres-cluster",
      "port": 5432,
      "database": "ares_prod",
      "username": "backup_user",
      "password": "backup_password",
      "s3Bucket": "my-backup-bucket",
      "s3Path": "postgresql/production"
    },
    {
      "type": "mysql",
      "host": "legacy-mysql.example.com",
      "port": 3306,
      "database": "legacy_db",
      "username": "backup_user",
      "password": "backup_password",
      "s3Bucket": "my-backup-bucket",
      "s3Path": "mysql/legacy"
    }
  ]
}
EOF

echo "Created test configuration with mixed cluster and direct host"
echo "Configuration:"
cat test-mixed-config.json

echo -e "\nTest configurations created successfully!"
echo "To test with actual AWS credentials, you can run:"
echo "  ./mneme -local <secret-arn-containing-cluster-config>"
echo ""
echo "Note: Make sure your AWS credentials have the following permissions:"
echo "  - RDS:DescribeDBClusters"
echo "  - RDS:DescribeDBInstances"
echo "  - SecretsManager:GetSecretValue"
echo "  - S3:PutObject"

# Cleanup
echo -e "\nCleaning up test files..."
rm -f test-cluster-config.json test-direct-config.json test-mixed-config.json
echo "Test files cleaned up."
