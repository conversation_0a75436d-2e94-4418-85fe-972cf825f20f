{"databases": [{"type": "postgresql", "host": "postgres.mneme-local.svc.cluster.local", "port": 5432, "database": "ares_test", "username": "postgres", "password": "postgres", "s3Bucket": "my-test-bucket", "s3Path": "postgresql/test"}, {"type": "mysql", "host": "mysql.mneme-local.svc.cluster.local", "port": 3306, "database": "tracker_test", "username": "mysql", "password": "mysql", "s3Bucket": "my-test-bucket", "s3Path": "mysql/test"}]}