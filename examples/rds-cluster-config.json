{"databases": [{"type": "postgresql", "clusterName": "my-postgres-cluster", "port": 5432, "database": "ares_prod", "username": "backup_user", "password": "backup_password", "s3Bucket": "my-backup-bucket", "s3Path": "postgresql/production"}, {"type": "mysql", "clusterName": "my-mysql-cluster", "port": 3306, "database": "tracker_prod", "username": "backup_user", "password": "backup_password", "s3Bucket": "my-backup-bucket", "s3Path": "mysql/production"}, {"type": "postgresql", "host": "direct-postgres.example.com", "port": 5432, "database": "legacy_db", "username": "backup_user", "password": "backup_password", "s3Bucket": "my-backup-bucket", "s3Path": "postgresql/legacy", "enabled": false}]}