{"databases": [{"type": "postgresql", "host": "postgres", "port": 5432, "database": "ares_sanitize", "username": "sanitize_user", "password": "sanitize_password", "s3Bucket": "my-backup-bucket", "s3SourcePath": "postgresql/backups", "s3DestinationPath": "postgresql/sanitized", "useLatestBackup": true, "sanitizedFilePrefix": "sanitized", "ecrImage": "586518255992.dkr.ecr.ca-central-1.amazonaws.com/ares-pandora", "sanitizeCommand": "mix", "sanitizeArgs": ["sanitize_db", "--sanitize"], "enabled": true}, {"type": "mysql", "host": "mysql", "port": 3306, "database": "tracker_sanitize", "username": "sanitize_user", "password": "sanitize_password", "s3Bucket": "my-backup-bucket", "s3SourcePath": "mysql/backups", "s3DestinationPath": "mysql/sanitized", "useLatestBackup": true, "sanitizedFilePrefix": "sanitized", "enabled": false}]}