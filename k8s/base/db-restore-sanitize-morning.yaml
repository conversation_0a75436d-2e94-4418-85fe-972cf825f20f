apiVersion: batch/v1
kind: CronJob
metadata:
  name: mneme-db-restore-sanitize-morning
spec:
  schedule: "0 8 * * *"  # Run at 8:00 AM Mountain Time
  timeZone: "America/Edmonton"
  jobTemplate:
    spec:
      ttlSecondsAfterFinished: 3600  # Delete succeeded jobs after 60 minutes
      template:
        spec:
          serviceAccountName: mneme
          containers:
          - name: db-restore-sanitize-morning
            image: mneme
            imagePullPolicy: IfNotPresent
            command: ["/usr/local/bin/entrypoint.sh"]
            args: ["db-restore-sanitize", "$(SECRETS_MANAGER_ARN)"]
            env:
            - name: AWS_REGION
              value: "ca-central-1"
            - name: SECRETS_MANAGER_ARN
              valueFrom:
                secretKeyRef:
                  name: mneme-secrets
                  key: restore-sanitize-morning-secret-arn
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: ARES_ECR_ENDPOINT
              value: "************.dkr.ecr.ca-central-1.amazonaws.com/ares-pandora"
            - name: TIME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: POSTGRES_HOST
              value: "postgres"
            - name: POSTGRES_PORT
              value: "5432"
            - name: POSTGRES_USER
              valueFrom:
                secretKeyRef:
                  name: db-credentials
                  key: postgres-user
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: db-credentials
                  key: postgres-password
            - name: POSTGRES_DB
              valueFrom:
                secretKeyRef:
                  name: db-credentials
                  key: postgres-db
            - name: MYSQL_HOST
              value: "mysql"
            - name: MYSQL_PORT
              value: "3306"
            - name: MYSQL_USER
              valueFrom:
                secretKeyRef:
                  name: db-credentials
                  key: mysql-user
            - name: MYSQL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: db-credentials
                  key: mysql-password
            - name: MYSQL_DATABASE
              valueFrom:
                secretKeyRef:
                  name: db-credentials
                  key: mysql-db
            # Add root credentials for database creation
            - name: MYSQL_ROOT_USERNAME
              value: "root"
            - name: MYSQL_ROOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: db-credentials
                  key: mysql-root-password
            # Add PostgreSQL root credentials for database creation
            - name: POSTGRES_ROOT_USERNAME
              valueFrom:
                secretKeyRef:
                  name: db-credentials
                  key: postgres-root-user
            - name: POSTGRES_ROOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: db-credentials
                  key: postgres-root-password
          restartPolicy: OnFailure
