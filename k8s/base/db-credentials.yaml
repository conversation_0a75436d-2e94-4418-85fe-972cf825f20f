apiVersion: v1
kind: Secret
metadata:
  name: db-credentials
type: Opaque
stringData:
  postgres-user: "sanitize_user"
  postgres-password: "sanitize_password"
  postgres-db: "sanitize_db"
  postgres-root-user: "postgres"
  postgres-root-password: "postgres_root_password"
  mysql-root-password: "sanitize_root_password"
  mysql-user: "sanitize_user"
  mysql-password: "sanitize_password"
  mysql-db: "sanitize_db"
