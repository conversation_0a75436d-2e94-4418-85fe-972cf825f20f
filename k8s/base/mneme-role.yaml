apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: job-creator
rules:
  - apiGroups: ["batch"]
    resources: ["jobs"]
    verbs: ["create", "get", "list", "watch", "delete"]
  - apiGroups: ["batch"]
    resources: ["jobs/status"]
    verbs: ["get", "list", "watch"]
  - apiGroups: [""] # Core API group
    resources: ["secrets", "pods"]
    verbs: ["get", "list"]
  - apiGroups: [""] # Core API group
    resources: ["configmaps"]
    verbs: ["create", "get", "list", "delete"]
  - apiGroups: ["apps"]
    resources: ["deployments"]
    verbs: ["get", "list", "patch", "update"]
  - apiGroups: ["apps"]
    resources: ["deployments/scale"]
    verbs: ["get", "update", "patch"]
