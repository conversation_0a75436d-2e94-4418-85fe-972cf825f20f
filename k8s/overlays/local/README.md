# Local Kubernetes Development Setup

This directory contains Kubernetes configuration for local development and testing of the mneme database backup and sanitization system.

## Prerequisites

- A local Kubernetes cluster (minikube, kind, Docker Desktop, etc.)
- kubectl installed and configured to use your local cluster
- Docker for building the image

## Setup

1. Build the Docker image and apply the Kubernetes configuration:

```bash
./apply-local-k8s.sh
```

2. For testing with AWS Secrets Manager, you can either:
   - Use a real AWS Secrets Manager secret if you have AWS credentials configured
   - Use the `-local` flag with a local file path

## Testing Job Creation

To test the job creation functionality, you can manually create a job from the CronJob:

```bash
kubectl -n mneme-local create job --from=cronjob/mneme-db-restore-sanitize test-restore-sanitize
```

Or to test the backup functionality:

```bash
kubectl -n mneme-local create job --from=cronjob/mneme-hourly test-hourly-backup
```

## Local Configuration

The local overlay includes:

1. A dedicated namespace (`mneme-local`)
2. Local database credentials
3. Modified CronJob schedule for easier testing
4. Configuration for using local services

## On-Demand Database Services

The MySQL and PostgreSQL services are configured to run on-demand, only when the sanitize job is running. This helps conserve resources when the services are not needed. The process works as follows:

1. By default, the database deployments have 0 replicas (not running)
2. When the sanitize job starts, it:
   - Scales up the database deployments to 1 replica using the Kubernetes API
   - Waits for the database services to be ready
   - Performs the sanitization tasks
   - After the main job completes, it:
     - Waits for all db-sanitize jobs (which handle MySQL tracker databases)
     - Waits for any spawned Ares sanitization jobs (for PostgreSQL ares databases)
     - Only scales down the database deployments back to 0 replicas after all jobs have completed

This ensures that database resources are only consumed when actually needed by the sanitization process. The scaling is handled directly by the Go code using the Kubernetes client-go library, which provides a more reliable and integrated approach than using external scripts.

## Database Cleanup

After the sanitization process is complete and the sanitized dump has been uploaded to S3, the temporary database will be automatically dropped from the MySQL or PostgreSQL server. This ensures that no temporary databases are left behind after the process is complete.

This cleanup happens in the following order:
1. Scale up database deployments
2. Download backup from S3
3. Restore database
4. Sanitize database:
   - For MySQL tracker databases: Sanitization happens directly in the db-sanitize job
   - For PostgreSQL ares databases: Spawns additional Ares sanitization jobs
5. Create sanitized dump
6. Upload sanitized dump to S3
7. **Drop temporary database**
8. Main job completes
9. Wait for all spawned jobs to complete:
   - Wait for all db-sanitize jobs
   - Wait for all Ares sanitization jobs
10. Scale down database deployments

## Using Local Configuration Files

If you want to test with a local configuration file instead of AWS Secrets Manager:

1. Create a pod that mounts the configuration file:

```bash
kubectl -n mneme-local create configmap mneme-test-config --from-file=config.json=local-test-config.json
```

2. Then run a job with the local flag:

```bash
kubectl -n mneme-local run mneme-local-test --image=mneme:latest --restart=Never -- /usr/local/bin/mneme -local /path/to/config.json
```

## Debugging

To view logs from a job:

```bash
kubectl -n mneme-local logs job/test-restore-sanitize
```

To describe a job:

```bash
kubectl -n mneme-local describe job/test-restore-sanitize
```
