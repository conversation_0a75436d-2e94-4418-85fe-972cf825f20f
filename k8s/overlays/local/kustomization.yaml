apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: mneme-local

resources:
- ../../base
- namespace.yaml

patches:
- path: patches/local-secrets.yaml
  target:
    kind: Secret
    name: mneme-secrets
- path: patches/local-db-credentials.yaml
  target:
    kind: Secret
    name: db-credentials
- path: patches/local-cronjob-patch.yaml
  target:
    kind: CronJob
    name: mneme-db-restore-sanitize

# Configure the image to use locally built image
images:
- name: mneme
  newName: mneme
  newTag: latest

# Add local configuration
configMapGenerator:
- name: mneme-config
  literals:
  - ENVIRONMENT=local
