apiVersion: batch/v1
kind: CronJob
metadata:
  name: mneme-db-restore-sanitize
spec:
  # For local testing, use a more frequent schedule or run manually
  schedule: "*/5 * * * *"  # Run every 5 minutes for testing
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: db-restore-sanitize
            # Add local environment variables for testing
            env:
            - name: LOG_LEVEL
              value: "debug"
            # Use local services
            - name: DB_HOST_POSTGRES
              value: "postgres.mneme-local.svc.cluster.local"
            - name: DB_HOST_MYSQL
              value: "mysql.mneme-local.svc.cluster.local"
            # Add a comment to document the database cleanup behavior
            - name: CLEANUP_COMMENT
              value: "Temporary databases will be automatically dropped after sanitization and upload to S3"
