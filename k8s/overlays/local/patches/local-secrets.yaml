apiVersion: v1
kind: Secret
metadata:
  name: mneme-secrets
type: Opaque
stringData:
  # For local testing, you can use a local file or a real ARN if you have AWS credentials configured
  hourly-secret-arn: "arn:aws:secretsmanager:region:account:secret:secret-name"
  daily-secret-arn: "arn:aws:secretsmanager:region:account:secret:secret-name"
  restore-sanitize-secret-arn: "arn:aws:secretsmanager:region:account:secret:secret-name"
