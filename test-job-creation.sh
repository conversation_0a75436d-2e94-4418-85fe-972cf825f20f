#!/bin/bash
set -e

# Ensure we're in the namespace
NAMESPACE="mneme-local"

# Check if the namespace exists
if ! kubectl get namespace $NAMESPACE &> /dev/null; then
  echo "Namespace $NAMESPACE does not exist. Please run ./apply-local-k8s.sh first."
  exit 1
fi

# Create a test job from the CronJob
JOB_NAME="test-restore-sanitize-$(date +%s)"
echo "Creating test job $JOB_NAME..."
kubectl -n $NAMESPACE create job --from=cronjob/mneme-db-restore-sanitize $JOB_NAME

echo "Job created. Monitoring logs..."
echo "Press Ctrl+C to stop monitoring logs."

# Wait for the pod to be created
POD_NAME=""
while [ -z "$POD_NAME" ]; do
  echo "Waiting for pod to be created..."
  POD_NAME=$(kubectl -n $NAMESPACE get pods --selector=job-name=$JOB_NAME -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
  if [ -z "$POD_NAME" ]; then
    sleep 2
  fi
done

echo "Pod $POD_NAME created. Streaming logs:"
kubectl -n $NAMESPACE logs -f $POD_NAME

# Note: The script will end when the logs command ends (when the pod completes or if the user presses Ctrl+C)

# After the job completes, check if the databases were dropped
echo "Checking if temporary databases were dropped..."

# Check PostgreSQL databases
echo "PostgreSQL databases:"
kubectl -n $NAMESPACE exec -it deployment/postgres -- psql -U postgres -c "\l"

# Check MySQL databases
echo "MySQL databases:"
kubectl -n $NAMESPACE exec -it deployment/mysql -- mysql -u root -proot -e "SHOW DATABASES;"

echo "If you don't see any temporary databases with names containing 'ares' or 'tracker', the cleanup was successful!"
