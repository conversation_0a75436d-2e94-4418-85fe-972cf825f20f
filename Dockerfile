# Use a multi-stage build to keep the final image small
FROM golang:1.24-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git

# Set the working directory
WORKDIR /app

# Copy the source code first
COPY . .

# Initialize go module if needed and download dependencies
RUN go mod tidy && go mod download

# Build both applications
RUN CGO_ENABLED=0 GOOS=linux go build -o mneme
RUN CGO_ENABLED=0 GOOS=linux go build -o db-restore-sanitize ./cmd/db-restore-sanitize

# Final stage
FROM debian:bullseye-slim

# Install required tools
# Add PostgreSQL 15 repository
RUN apt-get update && apt-get install -y \
    curl \
    gnupg \
    lsb-release \
    && curl -fsSL https://www.postgresql.org/media/keys/ACCC4CF8.asc | gpg --dearmor -o /usr/share/keyrings/postgresql-keyring.gpg \
    && echo "deb [signed-by=/usr/share/keyrings/postgresql-keyring.gpg] http://apt.postgresql.org/pub/repos/apt/ $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list

# Install PostgreSQL 15 client, MySQL client and other tools
# Also upgrade all packages to get latest security patches (including glibc fix)
RUN apt-get update && apt-get upgrade -y && apt-get install -y \
    postgresql-client-15 \
    default-mysql-client \
    gzip \
    curl \
    python3-pip \
    pv \
    && pip3 install awscli \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy the binaries from the builder stage
COPY --from=builder /app/mneme /usr/local/bin/
COPY --from=builder /app/db-restore-sanitize /usr/local/bin/

# No need for the wrapper script or kubectl anymore as we're using Go code for deployment scaling

# Create a simple wrapper script to handle different commands
COPY <<EOF /usr/local/bin/entrypoint.sh
#!/bin/sh
set -e

if [ "\$1" = "db-restore-sanitize" ]; then
  shift
  exec /usr/local/bin/db-restore-sanitize "\$@"
else
  exec /usr/local/bin/mneme "\$@"
fi
EOF

RUN chmod +x /usr/local/bin/entrypoint.sh

# Set the entrypoint to our wrapper script
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]