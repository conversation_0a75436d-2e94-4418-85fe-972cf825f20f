package sanitize

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/mneme/db-tools/pkg/config"
	"github.com/mneme/db-tools/pkg/k8s"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
)

// MySQLSanitizer handles sanitization of MySQL databases
type MySQLSanitizer struct {
	Host     string
	Port     int
	Database string
	Username string
	Password string
	// Kubernetes client for creating sanitization jobs
	K8sClient *kubernetes.Clientset
	// Configuration for sanitization jobs
	Config *config.RestoreDatabaseConfig
}

// NewMySQLSanitizer creates a new MySQL sanitizer with configuration
func NewMySQLSanitizer(host string, port int, database, username, password string, config *config.RestoreDatabaseConfig) *MySQLSanitizer {
	// Try to initialize Kubernetes client if we're running in a cluster
	var k8sClient *kubernetes.Clientset
	if os.Getenv("KUBERNETES_SERVICE_HOST") != "" {
		k8sConfig, err := rest.InClusterConfig()
		if err == nil {
			client, err := kubernetes.NewForConfig(k8sConfig)
			if err == nil {
				k8sClient = client
			} else {
				log.Printf("Warning: Failed to create Kubernetes client: %v", err)
			}
		} else {
			log.Printf("Warning: Failed to get in-cluster config: %v", err)
		}
	}

	return &MySQLSanitizer{
		Host:      host,
		Port:      port,
		Database:  database,
		Username:  username,
		Password:  password,
		K8sClient: k8sClient,
		Config:    config,
	}
}

// Connect establishes a connection to the MySQL database
func (s *MySQLSanitizer) Connect() (*sql.DB, error) {
	// Create the connection string without specifying authentication method
	connStr := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s",
		s.Username, s.Password, s.Host, s.Port, s.Database)

	db, err := sql.Open("mysql", connStr)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to MySQL: %v", err)
	}

	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to ping MySQL: %v", err)
	}

	return db, nil
}

// GetDatabaseURL returns the database connection string for MySQL in the required format
func (s *MySQLSanitizer) GetDatabaseURL() string {
	return fmt.Sprintf("Server=%s;Port=%d;Database=%s;Uid=%s;Pwd=%s;",
		s.Host, s.Port, s.Database, s.Username, s.Password)
}

// recreateView recreates a view with its original definition
func (s *MySQLSanitizer) recreateView(db *sql.DB, view string) error {
	log.Printf("Recreating view: %s", view)

	// Find query for creating view
	var viewDefinition string
	err := db.QueryRow(fmt.Sprintf("SELECT view_definition FROM information_schema.views WHERE table_name='%s'", view)).Scan(&viewDefinition)
	if err != nil {
		return fmt.Errorf("cannot find view definition for %s: %v", view, err)
	}

	// Create replacement view
	_, err = db.Exec(fmt.Sprintf("CREATE OR REPLACE VIEW %s AS %s", view, viewDefinition))
	if err != nil {
		return fmt.Errorf("cannot recreate view %s: %v", view, err)
	}

	log.Printf("Successfully recreated view: %s", view)
	return nil
}

// sanitizeTable sanitizes a table using the provided query
func (s *MySQLSanitizer) sanitizeTable(db *sql.DB, query string, table string) error {
	log.Printf("Sanitizing table: %s", table)

	// Create table from show create table to keep foreign keys
	var tableName, createTableSQL string
	err := db.QueryRow(fmt.Sprintf("SHOW CREATE TABLE %s", table)).Scan(&tableName, &createTableSQL)
	if err != nil {
		return fmt.Errorf("cannot show how table was created for %s: %v", table, err)
	}

	// Create temporary table for data
	_, err = db.Exec(fmt.Sprintf("CREATE TABLE temp LIKE %s", table))
	if err != nil {
		return fmt.Errorf("cannot create temp table for %s: %v", table, err)
	}

	// Add data to temporary table
	if query != "" {
		_, err := db.Exec(fmt.Sprintf("INSERT INTO temp %s", query))
		if err != nil {
			return fmt.Errorf("cannot insert data into temporary table for %s: %v", table, err)
		}
	}

	// Remove foreign key checks for deletion of original table
	_, err = db.Exec("SET FOREIGN_KEY_CHECKS=0;")
	if err != nil {
		return fmt.Errorf("cannot remove foreign key checks: %v", err)
	}

	// Delete original table before creating a new one due to foreign key checks
	_, err = db.Exec(fmt.Sprintf("DROP TABLE %s", table))
	if err != nil {
		return fmt.Errorf("cannot drop old table %s: %v", table, err)
	}

	// Create temporary table for schema and foreign keys
	createNewTableSQL := fmt.Sprintf("CREATE TABLE new %s", createTableSQL[strings.Index(createTableSQL, "("):])
	_, err = db.Exec(createNewTableSQL)
	if err != nil {
		return fmt.Errorf("cannot create new table for %s: %v", table, err)
	}

	// Workaround of foreign key checks
	_, err = db.Exec("INSERT INTO new SELECT * FROM temp")
	if err != nil {
		return fmt.Errorf("cannot insert into new table for %s: %v", table, err)
	}

	// Change new name back to original
	_, err = db.Exec(fmt.Sprintf("RENAME TABLE new TO %s", table))
	if err != nil {
		return fmt.Errorf("cannot rename table for %s: %v", table, err)
	}

	// No longer need temporary data
	_, err = db.Exec("DROP TABLE temp")
	if err != nil {
		return fmt.Errorf("cannot drop temp table for %s: %v", table, err)
	}

	log.Printf("Successfully sanitized table: %s", table)
	return nil
}

// anonEmail anonymizes email addresses in a table column
func (s *MySQLSanitizer) anonEmail(db *sql.DB, table string, column string) error {
	log.Printf("Anonymizing email in %s.%s", table, column)

	// anonymize email
	_, err := db.Exec(fmt.Sprintf("UPDATE %s SET %s=REPLACE(%s, LEFT(%s, POSITION('@' in %s) - 1), LEFT(uuid(), 8)) WHERE %s>''",
		table, column, column, column, column, column))
	if err != nil {
		return fmt.Errorf("failed to anonymize email in %s.%s: %v", table, column, err)
	}

	return nil
}

// anonString anonymizes string values in a table column
func (s *MySQLSanitizer) anonString(db *sql.DB, table string, column string) error {
	log.Printf("Anonymizing string in %s.%s", table, column)

	// anonymize any column
	_, err := db.Exec(fmt.Sprintf("UPDATE %s SET %s=REPLACE(%s, %s, LEFT(UUID(), (SELECT CHARACTER_MAXIMUM_LENGTH FROM INFORMATION_SCHEMA.COLUMNS WHERE column_name='%s' AND table_name='%s' AND table_schema='%s'))) WHERE %s>''",
		table, column, column, column, column, table, s.Database, column))
	if err != nil {
		return fmt.Errorf("failed to anonymize string in %s.%s: %v", table, column, err)
	}

	return nil
}

// anonNullible sets column values to NULL
func (s *MySQLSanitizer) anonNullible(db *sql.DB, table string, column string) error {
	log.Printf("Setting nullible values to NULL in %s.%s", table, column)

	// Check if the column is actually nullable
	isNullable, err := s.isColumnNullable(db, table, column)
	if err != nil {
		log.Printf("Warning: Could not determine if column %s.%s is nullable: %v", table, column, err)
		// Continue with the operation, but it might fail if the column is NOT NULL
	}

	if !isNullable {
		log.Printf("Warning: Column %s.%s is NOT NULL, using empty string instead", table, column)
		// Use empty string for NOT NULL columns
		_, err := db.Exec(fmt.Sprintf("UPDATE %s SET %s='' WHERE %s>''",
			table, column, column))
		if err != nil {
			return fmt.Errorf("failed to set empty string in %s.%s: %v", table, column, err)
		}
	} else {
		// Set column to NULL for nullable columns
		_, err := db.Exec(fmt.Sprintf("UPDATE %s SET %s=NULL WHERE %s>''",
			table, column, column))
		if err != nil {
			return fmt.Errorf("failed to set NULL in %s.%s: %v", table, column, err)
		}
	}

	return nil
}

// anonNotNullible sets column values to empty string
func (s *MySQLSanitizer) anonNotNullible(db *sql.DB, table string, column string) error {
	log.Printf("Setting not nullible values to empty string in %s.%s", table, column)

	// set col to empty string
	_, err := db.Exec(fmt.Sprintf("UPDATE %s SET %s='' WHERE %s>''",
		table, column, column))
	if err != nil {
		return fmt.Errorf("failed to set empty string in %s.%s: %v", table, column, err)
	}

	return nil
}

// anonPhone anonymizes phone numbers in a table column
func (s *MySQLSanitizer) anonPhone(db *sql.DB, table string, column string, styled bool) error {
	log.Printf("Anonymizing phone in %s.%s (styled: %v)", table, column, styled)

	// anonymize phone number
	var err error
	if styled {
		_, err = db.Exec(fmt.Sprintf("UPDATE %s SET %s=CONCAT(LPAD(FLOOR(RAND() * 1000), 3, '0'), '-', LPAD(FLOOR(RAND() * 1000), 3, '0'), '-', LPAD(FLOOR(RAND() * 10000), 4, '0')) WHERE %s>''",
			table, column, column))
	} else {
		_, err = db.Exec(fmt.Sprintf("UPDATE %s SET %s=LPAD(FLOOR(RAND() * 10000000000), 10, '0') WHERE %s>''",
			table, column, column))
	}
	if err != nil {
		return fmt.Errorf("failed to anonymize phone in %s.%s: %v", table, column, err)
	}

	return nil
}

// tableExists checks if a table exists in the database
func (s *MySQLSanitizer) tableExists(db *sql.DB, tableName string) (bool, error) {
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = ? AND table_name = ?",
		s.Database, tableName).Scan(&count)
	if err != nil {
		return false, fmt.Errorf("failed to check if table %s exists: %v", tableName, err)
	}
	return count > 0, nil
}

// isColumnNullable checks if a column in a table allows NULL values
func (s *MySQLSanitizer) isColumnNullable(db *sql.DB, table string, column string) (bool, error) {
	var isNullable string
	err := db.QueryRow("SELECT IS_NULLABLE FROM information_schema.columns WHERE table_schema = ? AND table_name = ? AND column_name = ?",
		s.Database, table, column).Scan(&isNullable)
	if err != nil {
		return false, fmt.Errorf("failed to check if column %s.%s is nullable: %v", table, column, err)
	}
	return isNullable == "YES", nil
}

// anonymizeTrackerData performs comprehensive anonymization on tracker database tables
func (s *MySQLSanitizer) anonymizeTrackerData(db *sql.DB) error {
	log.Printf("Performing comprehensive anonymization on tracker database %s", s.Database)

	// addresses
	exists, err := s.tableExists(db, "addresses")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonString(db, "addresses", "line_1"); err != nil {
			return err
		}
		if err := s.anonString(db, "addresses", "line_2"); err != nil {
			return err
		}
	}

	// applicants
	exists, err = s.tableExists(db, "applicants")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonString(db, "applicants", "name"); err != nil {
			return err
		}
		if err := s.anonString(db, "applicants", "current_employer_name"); err != nil {
			return err
		}
		if err := s.anonString(db, "applicants", "current_employer_job_title"); err != nil {
			return err
		}
		if err := s.anonNullible(db, "applicants", "applicant_notes"); err != nil {
			return err
		}
	}

	// assets
	exists, err = s.tableExists(db, "assets")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonString(db, "assets", "name"); err != nil {
			return err
		}
	}

	// borrowers
	exists, err = s.tableExists(db, "borrowers")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonString(db, "borrowers", "name"); err != nil {
			return err
		}
	}

	// brokerages
	exists, err = s.tableExists(db, "brokerages")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonString(db, "brokerages", "name"); err != nil {
			return err
		}
		if err := s.anonString(db, "brokerages", "address"); err != nil {
			return err
		}
		if err := s.anonPhone(db, "brokerages", "phone", true); err != nil {
			return err
		}
		if err := s.anonPhone(db, "brokerages", "fax", true); err != nil {
			return err
		}
	}

	// communications
	exists, err = s.tableExists(db, "communications")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonString(db, "communications", "title"); err != nil {
			return err
		}
		if err := s.anonNotNullible(db, "communications", "details"); err != nil {
			return err
		}
	}

	// concierge_loans
	exists, err = s.tableExists(db, "concierge_loans")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonNullible(db, "concierge_loans", "notes"); err != nil {
			return err
		}
		if err := s.anonNullible(db, "concierge_loans", "auction_notes"); err != nil {
			return err
		}
		if err := s.anonString(db, "concierge_loans", "lawyer_contact_info"); err != nil {
			return err
		}
	}

	// concierge_loan_payments
	exists, err = s.tableExists(db, "concierge_loan_payments")
	if err != nil {
		return err
	}
	if exists {
		// Use anonNotNullible instead of anonNullible since the notes column is defined as NOT NULL
		if err := s.anonNotNullible(db, "concierge_loan_payments", "notes"); err != nil {
			return err
		}
	}

	// deals
	exists, err = s.tableExists(db, "deals")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonString(db, "deals", "referred_by"); err != nil {
			return err
		}
		if err := s.anonNotNullible(db, "deals", "notes"); err != nil {
			return err
		}
		if err := s.anonNullible(db, "deals", "mobile_broker_notes"); err != nil {
			return err
		}
		if err := s.anonNotNullible(db, "deals", "raw_lead"); err != nil {
			return err
		}
		if err := s.anonNullible(db, "deals", "notes_to_doc_specialist"); err != nil {
			return err
		}
	}

	// deal_atlases
	exists, err = s.tableExists(db, "deal_atlases")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonString(db, "deal_atlases", "appraiser_name"); err != nil {
			return err
		}
		if err := s.anonString(db, "deal_atlases", "approval_name"); err != nil {
			return err
		}
		if err := s.anonString(db, "deal_atlases", "broker_first_name"); err != nil {
			return err
		}
		if err := s.anonString(db, "deal_atlases", "broker_last_name"); err != nil {
			return err
		}
		if err := s.anonString(db, "deal_atlases", "client_name1"); err != nil {
			return err
		}
		if err := s.anonString(db, "deal_atlases", "client_name2"); err != nil {
			return err
		}
		if err := s.anonString(db, "deal_atlases", "client_name3"); err != nil {
			return err
		}
		if err := s.anonString(db, "deal_atlases", "client_name4"); err != nil {
			return err
		}
		if err := s.anonEmail(db, "deal_atlases", "co_agent_email"); err != nil {
			return err
		}
		if err := s.anonString(db, "deal_atlases", "co_agent_name"); err != nil {
			return err
		}
		if err := s.anonString(db, "deal_atlases", "finalized_by"); err != nil {
			return err
		}
		if err := s.anonString(db, "deal_atlases", "legal_address"); err != nil {
			return err
		}
	}

	// deal_fee_splits
	exists, err = s.tableExists(db, "deal_fee_splits")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonNullible(db, "deal_fee_splits", "split_note"); err != nil {
			return err
		}
	}

	// deal_ratings
	exists, err = s.tableExists(db, "deal_ratings")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonNullible(db, "deal_ratings", "closer_note"); err != nil {
			return err
		}
	}

	// document_section_memos
	exists, err = s.tableExists(db, "document_section_memos")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonString(db, "document_section_memos", "custom_section_name"); err != nil {
			return err
		}
		if err := s.anonNotNullible(db, "document_section_memos", "agent_memo"); err != nil {
			return err
		}
		if err := s.anonNotNullible(db, "document_section_memos", "internal_memo"); err != nil {
			return err
		}
	}

	// email_bccs
	exists, err = s.tableExists(db, "email_bccs")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonEmail(db, "email_bccs", "bcc_email"); err != nil {
			return err
		}
		if err := s.anonNotNullible(db, "email_bccs", "bcc_name"); err != nil {
			return err
		}
	}

	// email_bounces
	exists, err = s.tableExists(db, "email_bounces")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonString(db, "email_bounces", "bounce_description"); err != nil {
			return err
		}
		if err := s.anonString(db, "email_bounces", "bounce_details"); err != nil {
			return err
		}
		if err := s.anonString(db, "email_bounces", "bounce_subject"); err != nil {
			return err
		}
	}

	// event_logs
	exists, err = s.tableExists(db, "event_logs")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonString(db, "event_logs", "data"); err != nil {
			return err
		}
	}

	// flinks_deal_data
	exists, err = s.tableExists(db, "flinks_deal_data")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonString(db, "flinks_deal_data", "full_name"); err != nil {
			return err
		}
		if err := s.anonEmail(db, "flinks_deal_data", "email"); err != nil {
			return err
		}
	}

	// follow_ups
	exists, err = s.tableExists(db, "follow_ups")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonNullible(db, "follow_ups", "notes"); err != nil {
			return err
		}
	}

	// ladder_system_hurdles
	exists, err = s.tableExists(db, "ladder_system_hurdles")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonNullible(db, "ladder_system_hurdles", "note"); err != nil {
			return err
		}
	}

	// lawyers
	exists, err = s.tableExists(db, "lawyers")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonString(db, "lawyers", "name"); err != nil {
			return err
		}
		if err := s.anonPhone(db, "lawyers", "phone", true); err != nil {
			return err
		}
	}

	// leads
	exists, err = s.tableExists(db, "leads")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonEmail(db, "leads", "email"); err != nil {
			return err
		}
		if err := s.anonPhone(db, "leads", "cell_phone", false); err != nil {
			return err
		}
		if err := s.anonPhone(db, "leads", "home_phone", false); err != nil {
			return err
		}
		if err := s.anonPhone(db, "leads", "work_phone", false); err != nil {
			return err
		}
		if err := s.anonString(db, "leads", "current_lender"); err != nil {
			return err
		}
		if err := s.anonNotNullible(db, "leads", "notes"); err != nil {
			return err
		}
		if err := s.anonNotNullible(db, "leads", "courtesy_agent_notes"); err != nil {
			return err
		}
	}

	// lead_sources
	exists, err = s.tableExists(db, "lead_sources")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonString(db, "lead_sources", "name"); err != nil {
			return err
		}
	}

	// ledger_deals
	exists, err = s.tableExists(db, "ledger_deals")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonNullible(db, "ledger_deals", "description"); err != nil {
			return err
		}
	}

	// lenders
	exists, err = s.tableExists(db, "lenders")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonString(db, "lenders", "underwriter"); err != nil {
			return err
		}
		if err := s.anonString(db, "lenders", "fulfilment"); err != nil {
			return err
		}
		if err := s.anonString(db, "lenders", "bdm"); err != nil {
			return err
		}
		if err := s.anonNotNullible(db, "lenders", "notes"); err != nil {
			return err
		}
	}

	// messages
	exists, err = s.tableExists(db, "messages")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonString(db, "messages", "message"); err != nil {
			return err
		}
	}

	// notes
	exists, err = s.tableExists(db, "notes")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonString(db, "notes", "notes"); err != nil {
			return err
		}
	}

	// properties
	exists, err = s.tableExists(db, "properties")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonString(db, "properties", "physical_street"); err != nil {
			return err
		}
		if err := s.anonString(db, "properties", "city"); err != nil {
			return err
		}
		if err := s.anonNullible(db, "properties", "property_description"); err != nil {
			return err
		}
		if err := s.anonNullible(db, "properties", "property_notes"); err != nil {
			return err
		}
	}

	// rates
	exists, err = s.tableExists(db, "rates")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonNotNullible(db, "rates", "notes"); err != nil {
			return err
		}
	}

	// realtor_deals
	exists, err = s.tableExists(db, "realtor_deals")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonString(db, "realtor_deals", "purchase_address"); err != nil {
			return err
		}
		if err := s.anonNullible(db, "realtor_deals", "mortgage_deal_notes"); err != nil {
			return err
		}
		if err := s.anonNotNullible(db, "realtor_deals", "buyer_status"); err != nil {
			return err
		}
	}

	// reminders
	exists, err = s.tableExists(db, "reminders")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonNotNullible(db, "reminders", "title"); err != nil {
			return err
		}
		if err := s.anonString(db, "reminders", "details"); err != nil {
			return err
		}
	}

	// stores
	exists, err = s.tableExists(db, "stores")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonPhone(db, "stores", "tollfree_phone", true); err != nil {
			return err
		}
		if err := s.anonPhone(db, "stores", "fax", true); err != nil {
			return err
		}
	}

	// store_bonus_periods
	exists, err = s.tableExists(db, "store_bonus_periods")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonNullible(db, "store_bonus_periods", "notes"); err != nil {
			return err
		}
	}

	// thinker_bonus_periods
	exists, err = s.tableExists(db, "thinker_bonus_periods")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonNullible(db, "thinker_bonus_periods", "note"); err != nil {
			return err
		}
	}

	// think_deal_change_requests
	exists, err = s.tableExists(db, "think_deal_change_requests")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonNotNullible(db, "think_deal_change_requests", "details"); err != nil {
			return err
		}
	}

	// think_deal_data
	exists, err = s.tableExists(db, "think_deal_data")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonNullible(db, "think_deal_data", "outstanding_conditions"); err != nil {
			return err
		}
	}

	// think_submissions
	exists, err = s.tableExists(db, "think_submissions")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonNullible(db, "think_submissions", "requested_investor_note"); err != nil {
			return err
		}
	}

	// transfers
	exists, err = s.tableExists(db, "transfers")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonNullible(db, "transfers", "giving_user_notes"); err != nil {
			return err
		}
		if err := s.anonNullible(db, "transfers", "receiving_user_notes"); err != nil {
			return err
		}
	}

	// trust_fund_transactions
	exists, err = s.tableExists(db, "trust_fund_transactions")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonNotNullible(db, "trust_fund_transactions", "notes"); err != nil {
			return err
		}
	}

	// unclaims
	exists, err = s.tableExists(db, "unclaims")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonNotNullible(db, "unclaims", "agent_notes"); err != nil {
			return err
		}
		if err := s.anonNullible(db, "unclaims", "admin_notes"); err != nil {
			return err
		}
	}

	// zeus_funder_deals
	exists, err = s.tableExists(db, "zeus_funder_deals")
	if err != nil {
		return err
	}
	if exists {
		if err := s.anonEmail(db, "zeus_funder_deals", "lawyer_email"); err != nil {
			return err
		}
	}

	log.Printf("Comprehensive anonymization completed successfully for tracker database %s", s.Database)
	return nil
}

// Sanitize performs sanitization on the MySQL database
func (s *MySQLSanitizer) Sanitize() error {
	db, err := s.Connect()
	if err != nil {
		return err
	}
	defer db.Close()

	log.Printf("Starting MySQL database sanitization check for database: %s", s.Database)

	// Check if we have sanitization configuration
	if s.Config != nil && s.Config.ECRImage != "" {
		log.Printf("Database %s has ECR image configured, proceeding with Kubernetes job sanitization", s.Database)

		// For databases with ECR configuration, create a Kubernetes job to perform the sanitization
		if s.K8sClient != nil {
			log.Printf("Creating sanitization job for database %s using ECR image %s", s.Database, s.Config.ECRImage)

			// Set the TIME environment variable if not already set
			if os.Getenv("TIME") == "" {
				// Load the America/Edmonton timezone for consistent timestamp generation
				loc, err := time.LoadLocation("America/Edmonton")
				if err != nil {
					log.Printf("Warning: Failed to load America/Edmonton timezone, using UTC: %v", err)
					os.Setenv("TIME", time.Now().Format("20060102150405"))
				} else {
					os.Setenv("TIME", time.Now().In(loc).Format("20060102150405"))
				}
			}

			// Create the tools instance
			tools := k8s.NewDatabaseJobManager(context.Background(), s.K8sClient)

			// Get the database URL
			dbURL := s.GetDatabaseURL()

			// Determine job name prefix from database name
			jobNamePrefix := strings.ToLower(s.Database)
			if strings.Contains(jobNamePrefix, "_") {
				// Use the first part before underscore as prefix
				parts := strings.Split(jobNamePrefix, "_")
				jobNamePrefix = parts[0]
			}

			// Create the sanitization job configuration
			jobConfig := k8s.DatabaseSanitizationJobConfig{
				DatabaseType:    "mysql",
				DatabaseURL:     dbURL,
				ECRImage:        s.Config.ECRImage,
				SanitizeCommand: s.Config.SanitizeCommand,
				SanitizeArgs:    s.Config.SanitizeArgs,
				JobNamePrefix:   jobNamePrefix,
			}

			// Create the sanitization job
			if err := tools.CreateDatabaseSanitizationJob(jobConfig); err != nil {
				return fmt.Errorf("failed to create sanitization job: %v", err)
			}

			log.Printf("Sanitization job completed for database %s", s.Database)
		} else {
			log.Printf("Kubernetes client not available, skipping sanitization for database %s", s.Database)
			log.Printf("WARNING: Database %s was not sanitized", s.Database)
		}

		log.Println("MySQL database sanitization completed successfully")
		return nil
	}

	// Check if the database name contains "tracker" for local sanitization
	if !strings.Contains(strings.ToLower(s.Database), "tracker") {
		log.Printf("No ECR image configured and database %s does not contain 'tracker', skipping sanitization", s.Database)
		return nil
	}

	log.Printf("Database %s contains 'tracker', proceeding with local sanitization", s.Database)

	// Add back foreign key checks at the end
	defer func() {
		_, err := db.Exec("SET FOREIGN_KEY_CHECKS=1")
		if err != nil {
			log.Printf("Warning: Failed to restore foreign key checks: %v", err)
		}
	}()

	// Check if we should perform full sanitization or partial
	var backupType string
	err = db.QueryRow("SELECT value FROM config WHERE name='backup_type'").Scan(&backupType)
	if err != nil {
		// If we can't determine the backup type, assume it's not a full backup
		log.Printf("Could not determine backup type, assuming partial sanitization: %v", err)
		backupType = "partial"
	}

	if backupType != "full" {
		log.Println("Performing partial sanitization (removing personal and unnecessary data)")

		// Remove all event_logs
		if err := s.sanitizeTable(db, "", "event_logs"); err != nil {
			return fmt.Errorf("failed to sanitize event_logs table: %v", err)
		}

		// Remove all logs
		if err := s.sanitizeTable(db, "", "logs"); err != nil {
			return fmt.Errorf("failed to sanitize logs table: %v", err)
		}

		// Perform comprehensive anonymization on tracker database tables
		if err := s.anonymizeTrackerData(db); err != nil {
			return fmt.Errorf("failed to anonymize tracker data: %v", err)
		}

		// Recreate views to reflect updated tables
		if err := s.recreateView(db, "deals_and_ledger_deal_values"); err != nil {
			return fmt.Errorf("failed to recreate deals_and_ledger_deal_values view: %v", err)
		}

		if err := s.recreateView(db, "users_accounts"); err != nil {
			return fmt.Errorf("failed to recreate users_accounts view: %v", err)
		}
	} else {
		log.Println("Performing full backup, skipping sanitization")
	}

	log.Println("MySQL database sanitization completed successfully")
	return nil
}
