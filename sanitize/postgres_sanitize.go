package sanitize

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	_ "github.com/lib/pq"
	"github.com/mneme/db-tools/pkg/config"
	"github.com/mneme/db-tools/pkg/k8s"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
)

// PostgresSanitizer handles sanitization of PostgreSQL databases
type PostgresSanitizer struct {
	Host     string
	Port     int
	Database string
	Username string
	Password string
	// Kubernetes client for creating sanitization jobs
	K8sClient *kubernetes.Clientset
	// Configuration for sanitization jobs
	Config *config.RestoreDatabaseConfig
}

// NewPostgresSanitizer creates a new PostgreSQL sanitizer with configuration
func NewPostgresSanitizer(host string, port int, database, username, password string, config *config.RestoreDatabaseConfig) *PostgresSanitizer {
	// Try to initialize Kubernetes client if we're running in a cluster
	var k8sClient *kubernetes.Clientset
	if os.Getenv("KUBERNETES_SERVICE_HOST") != "" {
		k8sConfig, err := rest.InClusterConfig()
		if err == nil {
			client, err := kubernetes.NewForConfig(k8sConfig)
			if err == nil {
				k8sClient = client
			} else {
				log.Printf("Warning: Failed to create Kubernetes client: %v", err)
			}
		} else {
			log.Printf("Warning: Failed to get in-cluster config: %v", err)
		}
	}

	return &PostgresSanitizer{
		Host:      host,
		Port:      port,
		Database:  database,
		Username:  username,
		Password:  password,
		K8sClient: k8sClient,
		Config:    config,
	}
}

// Connect establishes a connection to the PostgreSQL database
func (s *PostgresSanitizer) Connect() (*sql.DB, error) {
	connStr := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable",
		s.Host, s.Port, s.Username, s.Password, s.Database)

	db, err := sql.Open("postgres", connStr)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to PostgreSQL: %v", err)
	}

	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to ping PostgreSQL: %v", err)
	}

	return db, nil
}

// GetDatabaseURL returns the database URL for connecting to the PostgreSQL database
func (s *PostgresSanitizer) GetDatabaseURL() string {
	return fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=disable",
		s.Username, s.Password, s.Host, s.Port, s.Database)
}

// Sanitize performs sanitization on the PostgreSQL database
func (s *PostgresSanitizer) Sanitize() error {
	db, err := s.Connect()
	if err != nil {
		return err
	}
	defer db.Close()

	log.Printf("Starting PostgreSQL database sanitization check for database: %s", s.Database)

	// Check if we have sanitization configuration
	if s.Config == nil || s.Config.ECRImage == "" {
		log.Printf("No ECR image configured for database %s, skipping sanitization", s.Database)
		return nil
	}

	log.Printf("Database %s has ECR image configured, proceeding with sanitization", s.Database)

	// For databases with ECR configuration, create a Kubernetes job to perform the sanitization
	if s.K8sClient != nil {
		log.Printf("Creating sanitization job for database %s using ECR image %s", s.Database, s.Config.ECRImage)

		// Set the TIME environment variable if not already set
		if os.Getenv("TIME") == "" {
			// Load the America/Edmonton timezone for consistent timestamp generation
			loc, err := time.LoadLocation("America/Edmonton")
			if err != nil {
				log.Printf("Warning: Failed to load America/Edmonton timezone, using UTC: %v", err)
				os.Setenv("TIME", time.Now().Format("20060102150405"))
			} else {
				os.Setenv("TIME", time.Now().In(loc).Format("20060102150405"))
			}
		}

		// Create the tools instance
		tools := k8s.NewDatabaseJobManager(context.Background(), s.K8sClient)

		// Get the database URL
		dbURL := s.GetDatabaseURL()

		// Determine job name prefix from database name
		jobNamePrefix := strings.ToLower(s.Database)
		if strings.Contains(jobNamePrefix, "_") {
			// Use the first part before underscore as prefix
			parts := strings.Split(jobNamePrefix, "_")
			jobNamePrefix = parts[0]
		}

		// Create the sanitization job configuration
		jobConfig := k8s.DatabaseSanitizationJobConfig{
			DatabaseType:    "postgresql",
			DatabaseURL:     dbURL,
			ECRImage:        s.Config.ECRImage,
			SanitizeCommand: s.Config.SanitizeCommand,
			SanitizeArgs:    s.Config.SanitizeArgs,
			JobNamePrefix:   jobNamePrefix,
		}

		// Create the sanitization job
		if err := tools.CreateDatabaseSanitizationJob(jobConfig); err != nil {
			return fmt.Errorf("failed to create sanitization job: %v", err)
		}

		log.Printf("Sanitization job completed for database %s", s.Database)
	} else {
		log.Printf("Kubernetes client not available, skipping sanitization for database %s", s.Database)
		log.Printf("WARNING: Database %s was not sanitized", s.Database)
	}

	log.Println("PostgreSQL database sanitization completed successfully")
	return nil
}
