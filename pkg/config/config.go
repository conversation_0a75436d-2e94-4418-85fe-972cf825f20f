package config

import (
	"time"
)

// Common configuration structures for both backup and restore/sanitize operations

// DatabaseConfig represents the common configuration for a database
type DatabaseConfig struct {
	Type        string `json:"type"`        // "postgresql" or "mysql"
	Host        string `json:"host"`        // Direct endpoint or RDS cluster name
	ClusterName string `json:"clusterName"` // RDS cluster name (optional, takes precedence over Host for cluster discovery)
	Port        int    `json:"port"`
	Database    string `json:"database"`
	Username    string `json:"username"`
	Password    string `json:"password"`
	S3Bucket    string `json:"s3Bucket"`
	Enabled     *bool  `json:"enabled,omitempty"` // Whether this database is enabled for processing (default: true if not specified)
}

// BackupConfig represents the configuration for all database backups
type BackupConfig struct {
	Databases []BackupDatabaseConfig `json:"databases"`
}

// BackupDatabaseConfig extends DatabaseConfig with backup-specific fields
type BackupDatabaseConfig struct {
	DatabaseConfig
	S3Path string `json:"s3Path"` // Path within the bucket to store dumps
}

// RestoreConfig represents the configuration for database restore and sanitization
type RestoreConfig struct {
	Databases []RestoreDatabaseConfig `json:"databases"`
}

// RestoreDatabaseConfig extends DatabaseConfig with restore-specific fields
type RestoreDatabaseConfig struct {
	DatabaseConfig
	S3SourcePath        string `json:"s3SourcePath"`        // Path within the bucket to find the dump
	S3DestinationPath   string `json:"s3DestinationPath"`   // Path to upload the sanitized dump
	BackupFileName      string `json:"backupFileName"`      // Optional specific backup file to restore
	UseLatestBackup     bool   `json:"useLatestBackup"`     // Whether to use the latest backup in the S3 path
	SanitizedFilePrefix string `json:"sanitizedFilePrefix"` // Prefix for the sanitized file name
	Enabled             *bool  `json:"enabled,omitempty"`   // Whether this database is enabled for processing (default: true if not specified)
	// PostgreSQL sanitization job configuration
	ECRImage        string   `json:"ecrImage,omitempty"`        // ECR image to use for sanitization job
	SanitizeCommand string   `json:"sanitizeCommand,omitempty"` // Command to run in the sanitization job
	SanitizeArgs    []string `json:"sanitizeArgs,omitempty"`    // Arguments for the sanitization command
}

// DumpResult represents the result of a database dump operation
type DumpResult struct {
	DatabaseName string
	FilePath     string
	Error        error
	StartTime    time.Time
	EndTime      time.Time
}
