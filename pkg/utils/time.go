package utils

import (
	"log"
	"time"
)

// GetTimestampInTimezone returns a timestamp formatted in the America/Edmonton timezone
func GetTimestampInTimezone() string {
	// Load the America/Edmonton timezone
	loc, err := time.LoadLocation("America/Edmonton")
	if err != nil {
		log.Printf("Warning: Failed to load America/Edmonton timezone, using UTC: %v", err)
		return time.Now().Format("20060102150405")
	}
	return time.Now().In(loc).Format("20060102150405")
}
