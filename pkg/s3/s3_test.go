package s3

import (
	"path/filepath"
	"strings"
	"testing"
)

// Test the database name extraction logic from local filenames
func TestDatabaseNameExtraction(t *testing.T) {
	tests := []struct {
		name        string
		localFile   string
		expectedDB  string
		expectedExt string
		description string
	}{
		{
			name:        "MySQL backup file",
			localFile:   "tracker_20250512151059.sql.gz",
			expectedDB:  "tracker",
			expectedExt: ".sql.gz",
			description: "Should extract 'tracker' from MySQL backup filename",
		},
		{
			name:        "PostgreSQL backup file",
			localFile:   "ares_20250512151059.dump",
			expectedDB:  "ares",
			expectedExt: ".dump",
			description: "Should extract 'ares' from PostgreSQL backup filename",
		},
		{
			name:        "Complex database name with underscores",
			localFile:   "tracker_prod_db_20250512151059.sql.gz",
			expectedDB:  "tracker",
			expectedExt: ".sql.gz",
			description: "Should extract first part before underscore as database name",
		},
		{
			name:        "Single word database name",
			localFile:   "mydb_20250512151059.sql.gz",
			expectedDB:  "mydb",
			expectedExt: ".sql.gz",
			description: "Should handle single word database names",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test extension detection
			var ext string
			localFileName := filepath.Base(tt.localFile)
			if strings.HasSuffix(localFileName, ".sql.gz") {
				ext = ".sql.gz"
			} else {
				ext = filepath.Ext(tt.localFile)
			}

			if ext != tt.expectedExt {
				t.Errorf("Extension detection failed: expected %s, got %s", tt.expectedExt, ext)
			}

			// Test database name extraction
			var dbName string
			if strings.HasSuffix(localFileName, ".sql.gz") {
				// MySQL file: remove .sql.gz and extract database name before timestamp
				baseName := strings.TrimSuffix(localFileName, ".sql.gz")
				// Split by underscore and take the first part as database name
				parts := strings.Split(baseName, "_")
				if len(parts) > 0 {
					dbName = parts[0]
				}
			} else if strings.HasSuffix(localFileName, ".dump") {
				// PostgreSQL file: remove .dump and extract database name before timestamp
				baseName := strings.TrimSuffix(localFileName, ".dump")
				// Split by underscore and take the first part as database name
				parts := strings.Split(baseName, "_")
				if len(parts) > 0 {
					dbName = parts[0]
				}
			}

			if dbName != tt.expectedDB {
				t.Errorf("Database name extraction failed: expected %s, got %s", tt.expectedDB, dbName)
			}
		})
	}
}

// Test the latest key generation logic
func TestLatestKeyGeneration(t *testing.T) {
	tests := []struct {
		name        string
		s3Path      string
		localFile   string
		expectedKey string
		description string
	}{
		{
			name:        "MySQL backup with unsanitized path",
			s3Path:      "prod/tracker/unsanitized",
			localFile:   "tracker_20250512151059.sql.gz",
			expectedKey: "prod/tracker/tracker_unsanitized_latest.sql.gz",
			description: "Should create correct latest key for MySQL backup",
		},
		{
			name:        "PostgreSQL backup with sanitized path",
			s3Path:      "dev/hourly/ares-sanitized",
			localFile:   "ares_20250512151059.dump",
			expectedKey: "dev/hourly/ares_ares-sanitized_latest.dump",
			description: "Should create correct latest key for PostgreSQL backup",
		},
		{
			name:        "MySQL backup with simple path",
			s3Path:      "backups",
			localFile:   "mydb_20250512151059.sql.gz",
			expectedKey: "mydb_backups_latest.sql.gz",
			description: "Should handle simple S3 paths",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Simulate the logic from UploadFile function
			var ext string
			localFileName := filepath.Base(tt.localFile)
			if strings.HasSuffix(localFileName, ".sql.gz") {
				ext = ".sql.gz"
			} else {
				ext = filepath.Ext(tt.localFile)
			}

			// Extract the database name from the local filename
			var dbName string
			if strings.HasSuffix(localFileName, ".sql.gz") {
				// MySQL file: remove .sql.gz and extract database name before timestamp
				baseName := strings.TrimSuffix(localFileName, ".sql.gz")
				// Split by underscore and take the first part as database name
				parts := strings.Split(baseName, "_")
				if len(parts) > 0 {
					dbName = parts[0]
				}
			} else if strings.HasSuffix(localFileName, ".dump") {
				// PostgreSQL file: remove .dump and extract database name before timestamp
				baseName := strings.TrimSuffix(localFileName, ".dump")
				// Split by underscore and take the first part as database name
				parts := strings.Split(baseName, "_")
				if len(parts) > 0 {
					dbName = parts[0]
				}
			} else {
				// Fallback: use the directory name
				dirPath := filepath.Dir(tt.s3Path)
				dbName = filepath.Base(dirPath)
			}

			// Extract the directory and the last component
			dirPath := filepath.Dir(tt.s3Path)
			lastComponent := filepath.Base(tt.s3Path)

			// Create the latest key with database name included in prefix
			latestKey := filepath.Join(dirPath, dbName+"_"+lastComponent+"_latest"+ext)

			if latestKey != tt.expectedKey {
				t.Errorf("Latest key generation failed: expected %s, got %s", tt.expectedKey, latestKey)
			}
		})
	}
}
