package s3

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"

	"github.com/mneme/db-tools/pkg/config"
	"github.com/mneme/db-tools/pkg/utils"
)

// Progress reporting has been removed to avoid issues with S3 uploads

// UploadFile uploads a file to S3
func UploadFile(s3Client *s3.Client, bucket, s3Path, localFile string) error {
	log.Printf("Uploading file to S3: %s", localFile)

	// Create S3 key with path
	s3Key := filepath.Join(s3Path, filepath.Base(localFile))
	log.Printf("Uploading to S3: s3://%s/%s", bucket, s3Key)

	// Open the file
	file, err := os.Open(localFile)
	if err != nil {
		return fmt.Errorf("failed to open file: %v", err)
	}
	defer file.Close()

	// Upload to S3 without progress tracking
	_, err = s3Client.PutObject(context.TODO(), &s3.PutObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(s3Key),
		Body:   file,
	})
	if err != nil {
		return fmt.Errorf("failed to upload to S3: %v", err)
	}

	log.Printf("Successfully uploaded file to s3://%s/%s", bucket, s3Key)

	// Create a _latest version of the file
	// We need to reopen the file since the previous read might have consumed it
	file, err = os.Open(localFile)
	if err != nil {
		log.Printf("Warning: failed to open file for _latest upload: %v", err)
		return nil // Continue without error as the main upload succeeded
	}
	defer file.Close()

	// Determine the file extension - handle .sql.gz properly for MySQL files
	var ext string
	localFileName := filepath.Base(localFile)
	if strings.HasSuffix(localFileName, ".sql.gz") {
		ext = ".sql.gz"
	} else {
		ext = filepath.Ext(localFile)
	}

	// Extract the database name from the local filename
	// For files like "tracker_20250512151059.sql.gz", extract "tracker"
	var dbName string
	if strings.HasSuffix(localFileName, ".sql.gz") {
		// MySQL file: remove .sql.gz and extract original database name
		baseName := strings.TrimSuffix(localFileName, ".sql.gz")
		dbName = extractOriginalDatabaseName(baseName)
	} else if strings.HasSuffix(localFileName, ".dump") {
		// PostgreSQL file: remove .dump and extract original database name
		baseName := strings.TrimSuffix(localFileName, ".dump")
		dbName = extractOriginalDatabaseName(baseName)
	} else {
		// Fallback: use the directory name
		dirPath := filepath.Dir(s3Path)
		dbName = filepath.Base(dirPath)
	}

	// For paths like "prod/tracker/unsanitized", we want to create "prod/tracker/tracker_unsanitized_latest.sql.gz"
	// Extract the directory and the last component
	dirPath := filepath.Dir(s3Path)
	lastComponent := filepath.Base(s3Path)

	// Create the latest key with database name as prefix
	latestKey := filepath.Join(dirPath, fmt.Sprintf("%s_%s_latest%s", dbName, lastComponent, ext))

	log.Printf("Uploading latest version to S3: s3://%s/%s", bucket, latestKey)

	// Upload the _latest version
	_, err = s3Client.PutObject(context.TODO(), &s3.PutObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(latestKey),
		Body:   file,
	})
	if err != nil {
		log.Printf("Warning: failed to upload _latest version to S3: %v", err)
		return nil // Continue without error as the main upload succeeded
	}

	log.Printf("Successfully uploaded latest version to s3://%s/%s", bucket, latestKey)
	return nil
}

// UploadSanitizedDump uploads a sanitized dump to S3 with a specific naming convention
func UploadSanitizedDump(s3Client *s3.Client, dbConfig config.RestoreDatabaseConfig, sanitizedDumpFile string) error {
	log.Printf("Uploading sanitized dump to S3: %s", sanitizedDumpFile)

	// Generate a timestamp for the S3 object name using America/Edmonton timezone
	timestamp := utils.GetTimestampInTimezone()

	// Determine the file extension based on database type
	var fileExt string
	if dbConfig.Type == "postgresql" {
		fileExt = ".dump"
	} else if dbConfig.Type == "mysql" {
		fileExt = ".sql.gz"
	} else {
		return fmt.Errorf("unsupported database type: %s", dbConfig.Type)
	}

	// Create the S3 object key
	prefix := dbConfig.SanitizedFilePrefix
	if prefix == "" {
		prefix = "sanitized"
	}

	s3Key := filepath.Join(
		dbConfig.S3DestinationPath,
		fmt.Sprintf("%s_%s_%s%s", prefix, dbConfig.Database, timestamp, fileExt),
	)

	// Open the sanitized dump file
	file, err := os.Open(sanitizedDumpFile)
	if err != nil {
		return fmt.Errorf("failed to open sanitized dump file: %v", err)
	}
	defer file.Close()

	// Upload the file to S3 without progress tracking
	_, err = s3Client.PutObject(context.TODO(), &s3.PutObjectInput{
		Bucket: aws.String(dbConfig.S3Bucket),
		Key:    aws.String(s3Key),
		Body:   file,
	})
	if err != nil {
		return fmt.Errorf("failed to upload sanitized dump to S3: %v", err)
	}

	log.Printf("Successfully uploaded sanitized dump to S3: s3://%s/%s", dbConfig.S3Bucket, s3Key)

	// Create a _latest version of the file
	// We need to reopen the file since the previous read might have consumed it
	file, err = os.Open(sanitizedDumpFile)
	if err != nil {
		log.Printf("Warning: failed to open file for _latest upload: %v", err)
		return nil // Continue without error as the main upload succeeded
	}
	defer file.Close()

	// For paths like "dev/hourly/ares-sanitized", we want to create "dev/hourly/ares-sanitized_latest.dump"
	// Extract the directory and the last component
	dirPath := filepath.Dir(strings.TrimSuffix(dbConfig.S3DestinationPath, "/"))
	lastComponent := filepath.Base(strings.TrimSuffix(dbConfig.S3DestinationPath, "/"))

	// Extract the original database name (remove timestamp if present)
	// Database names like "servicing_prod_heloc_20250603010002" should become "servicing_prod_heloc"
	originalDBName := extractOriginalDatabaseName(dbConfig.Database)

	// Create the latest key with original database name included in prefix
	latestKey := filepath.Join(
		dirPath,
		fmt.Sprintf("%s_%s_latest%s", originalDBName, lastComponent, fileExt),
	)

	log.Printf("Uploading latest version to S3: s3://%s/%s", dbConfig.S3Bucket, latestKey)

	// Upload the _latest version
	_, err = s3Client.PutObject(context.TODO(), &s3.PutObjectInput{
		Bucket: aws.String(dbConfig.S3Bucket),
		Key:    aws.String(latestKey),
		Body:   file,
	})
	if err != nil {
		log.Printf("Warning: failed to upload _latest version to S3: %v", err)
		return nil // Continue without error as the main upload succeeded
	}

	log.Printf("Successfully uploaded latest version to s3://%s/%s", dbConfig.S3Bucket, latestKey)
	return nil
}

// DownloadLatestBackup finds and downloads the latest backup file from S3
func DownloadLatestBackup(s3Client *s3.Client, dbConfig *config.RestoreDatabaseConfig) (string, error) {
	var s3Key string
	var fileName string

	if dbConfig.UseLatestBackup {
		// Find the latest backup file in the S3 path
		log.Printf("Finding latest backup in S3 bucket %s, path %s", dbConfig.S3Bucket, dbConfig.S3SourcePath)

		// List objects in the S3 path
		resp, err := s3Client.ListObjectsV2(context.TODO(), &s3.ListObjectsV2Input{
			Bucket: aws.String(dbConfig.S3Bucket),
			Prefix: aws.String(dbConfig.S3SourcePath),
		})
		if err != nil {
			return "", fmt.Errorf("failed to list objects in S3: %v", err)
		}

		if len(resp.Contents) == 0 {
			return "", fmt.Errorf("no backup files found in S3 path: %s", dbConfig.S3SourcePath)
		}

		// Find the latest file by LastModified timestamp
		var latestFile *types.Object
		for _, obj := range resp.Contents {
			if latestFile == nil || obj.LastModified.After(*latestFile.LastModified) {
				// Check if the file has the right extension for the database type
				if (dbConfig.Type == "postgresql" && strings.HasSuffix(*obj.Key, ".dump")) ||
					(dbConfig.Type == "mysql" && strings.HasSuffix(*obj.Key, ".sql.gz")) {
					latestFile = &obj
				}
			}
		}

		if latestFile == nil {
			return "", fmt.Errorf("no suitable backup files found in S3 path: %s", dbConfig.S3SourcePath)
		}

		s3Key = *latestFile.Key
		fileName = filepath.Base(s3Key)
		log.Printf("Found latest backup file: %s", s3Key)
	} else {
		// Use the specified backup file
		if dbConfig.BackupFileName == "" {
			return "", fmt.Errorf("backup file name is required when not using latest backup")
		}
		fileName = dbConfig.BackupFileName
		s3Key = filepath.Join(dbConfig.S3SourcePath, fileName)
	}

	// Extract database name from filename
	dbName := extractDatabaseNameFromFilename(fileName, dbConfig.Type)
	log.Printf("Using database name from filename: %s", dbName)

	// Update the database name in the config
	originalDBName := dbConfig.Database
	dbConfig.Database = dbName

	// Download the file
	localFile, err := DownloadFile(s3Client, dbConfig.S3Bucket, s3Key, dbName)

	// If there was an error, restore the original database name
	if err != nil {
		dbConfig.Database = originalDBName
	}

	return localFile, err
}

// extractDatabaseNameFromFilename extracts the database name from the backup filename
func extractDatabaseNameFromFilename(fileName string, dbType string) string {
	// Remove file extension
	baseName := fileName
	if dbType == "postgresql" {
		baseName = strings.TrimSuffix(baseName, ".dump")
	} else if dbType == "mysql" {
		baseName = strings.TrimSuffix(baseName, ".sql.gz")
	}

	// For filenames like "database_20250512151059.sql.gz", extract "database_20250512151059"
	return baseName
}

// DownloadFile downloads a file from S3
func DownloadFile(s3Client *s3.Client, bucket, s3Key, databaseName string) (string, error) {
	log.Printf("Downloading file from S3: s3://%s/%s", bucket, s3Key)

	// Create a temporary file to store the downloaded file
	tempFile, err := os.CreateTemp("", fmt.Sprintf("%s-backup-*", databaseName))
	if err != nil {
		return "", fmt.Errorf("failed to create temporary file: %v", err)
	}
	defer tempFile.Close()

	// Download the file from S3
	result, err := s3Client.GetObject(context.TODO(), &s3.GetObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(s3Key),
	})
	if err != nil {
		os.Remove(tempFile.Name()) // Clean up the temp file if download fails
		return "", fmt.Errorf("failed to get object from S3: %v", err)
	}
	defer result.Body.Close()

	// Get the size of the file for logging
	size := *result.ContentLength
	log.Printf("Starting download of %d bytes", size)

	// Copy the S3 object content to the temporary file directly without progress tracking
	_, err = tempFile.ReadFrom(result.Body)
	if err != nil {
		os.Remove(tempFile.Name()) // Clean up the temp file if copy fails
		return "", fmt.Errorf("failed to write to temporary file: %v", err)
	}

	log.Printf("Successfully downloaded file to %s", tempFile.Name())
	return tempFile.Name(), nil
}

// extractOriginalDatabaseName removes timestamp suffixes from database names
// Examples:
//   - "servicing_prod_heloc_20250603010002" -> "servicing_prod_heloc"
//   - "ares_20250603010002" -> "ares"
//   - "tracker" -> "tracker" (no change if no timestamp)
func extractOriginalDatabaseName(dbName string) string {
	// Pattern to match timestamp suffix: underscore followed by 14 digits (YYYYMMDDHHMMSS)
	timestampPattern := regexp.MustCompile(`_\d{14}$`)

	// Remove the timestamp suffix if it exists
	return timestampPattern.ReplaceAllString(dbName, "")
}
