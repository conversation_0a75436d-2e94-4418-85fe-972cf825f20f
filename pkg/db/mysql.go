package db

import (
	"bufio"
	"compress/gzip"
	"fmt"
	"io"
	"log"
	"os"
	"os/exec"
	"strings"
	"time"

	"github.com/mneme/db-tools/pkg/config"
	"github.com/mneme/db-tools/pkg/utils"
)

// MySQLBackup creates a backup of a MySQL database
func MySQLBackup(dbConfig config.BackupDatabaseConfig) (string, error) {
	log.Printf("Starting MySQL backup for database %s", dbConfig.Database)

	// Create a timestamp for the backup file using America/Edmonton timezone
	timestamp := utils.GetTimestampInTimezone()
	backupFile := fmt.Sprintf("%s_%s.sql.gz", dbConfig.Database, timestamp)

	// Create the mysqldump command
	cmd := exec.Command("mysqldump",
		"-h", dbConfig.Host,
		"-P", fmt.Sprintf("%d", dbConfig.Port),
		"-u", dbConfig.Username,
		fmt.Sprintf("-p%s", dbConfig.Password),
		dbConfig.Database)

	// Pipe mysqldump output through gzip
	gzipCmd := exec.Command("gzip")
	gzipCmd.Stdin, _ = cmd.StdoutPipe()
	gzipCmd.Stdout, _ = os.Create(backupFile)

	// Start gzip first
	if err := gzipCmd.Start(); err != nil {
		return "", fmt.Errorf("failed to start gzip: %v", err)
	}

	// Then run mysqldump
	if err := cmd.Run(); err != nil {
		return "", fmt.Errorf("failed to create backup: %v", err)
	}

	// Wait for gzip to finish
	if err := gzipCmd.Wait(); err != nil {
		return "", fmt.Errorf("failed to compress backup: %v", err)
	}

	log.Printf("MySQL backup completed for database %s: %s", dbConfig.Database, backupFile)
	return backupFile, nil
}

// CheckMySQLConnection checks if the MySQL server is accessible with retry logic
func CheckMySQLConnection(dbConfig config.RestoreDatabaseConfig) error {
	log.Printf("Checking MySQL connection to %s:%d", dbConfig.Host, dbConfig.Port)

	// Retry configuration
	maxRetries := 15
	initialDelay := 5 * time.Second
	maxDelay := 60 * time.Second
	totalTimeout := 5 * time.Minute

	startTime := time.Now()
	delay := initialDelay

	for attempt := 1; attempt <= maxRetries; attempt++ {
		// Check if total timeout has been reached
		if time.Since(startTime) > totalTimeout {
			return fmt.Errorf("timeout reached after %v while waiting for MySQL connection to %s:%d", totalTimeout, dbConfig.Host, dbConfig.Port)
		}

		// Create a simple connection check command
		checkCmd := exec.Command("mysql",
			"-h", dbConfig.Host,
			"-P", fmt.Sprintf("%d", dbConfig.Port),
			"-u", dbConfig.Username,
			fmt.Sprintf("-p%s", dbConfig.Password),
			"-e", "SELECT 1")

		// Suppress stderr for retry attempts to avoid log spam
		if attempt < maxRetries {
			checkCmd.Stderr = nil
		} else {
			checkCmd.Stderr = os.Stderr
		}

		if err := checkCmd.Run(); err != nil {
			if attempt == maxRetries {
				return fmt.Errorf("failed to connect to MySQL server after %d attempts: %v", maxRetries, err)
			}

			log.Printf("MySQL connection attempt %d/%d failed, retrying in %v...", attempt, maxRetries, delay)
			time.Sleep(delay)

			// Exponential backoff with max delay
			delay *= 2
			if delay > maxDelay {
				delay = maxDelay
			}
			continue
		}

		log.Printf("MySQL connection to %s:%d successful (attempt %d/%d)", dbConfig.Host, dbConfig.Port, attempt, maxRetries)
		return nil
	}

	return fmt.Errorf("failed to connect to MySQL server after %d attempts", maxRetries)
}

// MySQLRestore restores a MySQL database from a backup file
func MySQLRestore(dbConfig config.RestoreDatabaseConfig, backupFile string) error {
	log.Printf("Restoring MySQL database %s from backup %s", dbConfig.Database, backupFile)

	// Get file size for progress estimation
	fileInfo, err := os.Stat(backupFile)
	if err != nil {
		log.Printf("Warning: Could not get backup file size: %v", err)
	} else {
		log.Printf("Starting MySQL restore of %d bytes", fileInfo.Size())
	}

	// Start a timer to track progress
	startTime := time.Now()
	log.Printf("MySQL restore started at %s", startTime.Format(time.RFC3339))

	// First, create the database if it doesn't exist
	// Try to use root user for database creation if available
	rootUsername := os.Getenv("MYSQL_ROOT_USERNAME")
	rootPassword := os.Getenv("MYSQL_ROOT_PASSWORD")

	// If root credentials are not provided, fall back to the regular user
	username := dbConfig.Username
	password := dbConfig.Password

	if rootUsername != "" && rootPassword != "" {
		log.Printf("Using root credentials to create database %s", dbConfig.Database)
		username = rootUsername
		password = rootPassword
	} else {
		log.Printf("Using regular credentials to create database %s", dbConfig.Database)
	}

	createDBCmd := exec.Command("mysql",
		"-h", dbConfig.Host,
		"-P", fmt.Sprintf("%d", dbConfig.Port),
		"-u", username,
		fmt.Sprintf("-p%s", password),
		"-e", fmt.Sprintf("CREATE DATABASE IF NOT EXISTS `%s`", dbConfig.Database))

	createDBCmd.Stdout = os.Stdout
	createDBCmd.Stderr = os.Stderr

	log.Printf("Creating database %s if it doesn't exist", dbConfig.Database)
	if err := createDBCmd.Run(); err != nil {
		return fmt.Errorf("failed to create database: %v", err)
	}

	var cmd *exec.Cmd

	if strings.HasSuffix(backupFile, ".gz") {
		log.Printf("Detected gzip file, using Go's built-in gzip library for faster decompression")

		// Create a temporary file for the decompressed SQL
		tempSQLFile, err := os.CreateTemp("", fmt.Sprintf("%s-decompressed-*.sql", dbConfig.Database))
		if err != nil {
			return fmt.Errorf("failed to create temporary file for decompressed SQL: %v", err)
		}
		tempSQLPath := tempSQLFile.Name()
		tempSQLFile.Close()          // Close it so we can reopen it for writing
		defer os.Remove(tempSQLPath) // Clean up when done

		// Open the gzip file
		gzipFile, err := os.Open(backupFile)
		if err != nil {
			return fmt.Errorf("failed to open gzip file: %v", err)
		}
		defer gzipFile.Close()

		// Create a gzip reader
		gzipReader, err := gzip.NewReader(gzipFile)
		if err != nil {
			return fmt.Errorf("failed to create gzip reader: %v", err)
		}
		defer gzipReader.Close()

		// Open the output file
		outputFile, err := os.Create(tempSQLPath)
		if err != nil {
			return fmt.Errorf("failed to create output file: %v", err)
		}
		defer outputFile.Close()

		// Use buffered writer for better performance
		bufWriter := bufio.NewWriter(outputFile)

		// Start a timer for decompression
		decompressStart := time.Now()
		log.Printf("Starting decompression of gzip file")

		// Copy the decompressed data to the file with a buffer
		_, err = io.Copy(bufWriter, gzipReader)
		if err != nil {
			return fmt.Errorf("failed to decompress data: %v", err)
		}

		// Flush the buffer to ensure all data is written
		if err := bufWriter.Flush(); err != nil {
			return fmt.Errorf("failed to flush buffer: %v", err)
		}

		// Close files explicitly before running mysql
		outputFile.Close()
		gzipReader.Close()
		gzipFile.Close()

		decompressDuration := time.Since(decompressStart)
		log.Printf("Decompression completed in %s", decompressDuration)

		// Get file size for logging
		fileInfo, err := os.Stat(tempSQLPath)
		if err != nil {
			log.Printf("Warning: Could not get decompressed file size: %v", err)
		} else {
			log.Printf("Decompressed file size: %d bytes", fileInfo.Size())
		}

		// Now run mysql with the decompressed file using pv for progress reporting
		log.Printf("Running mysql with pv to import the decompressed data with progress reporting")

		// Get file size for pv
		fileInfo, err = os.Stat(tempSQLPath)
		if err != nil {
			log.Printf("Warning: Could not get decompressed file size for pv: %v", err)
			// Fall back to regular import without pv
			mysqlCmd := exec.Command("mysql",
				"-h", dbConfig.Host,
				"-P", fmt.Sprintf("%d", dbConfig.Port),
				"-u", dbConfig.Username,
				fmt.Sprintf("-p%s", dbConfig.Password),
				"--binary-mode=1", // Add binary mode to handle null bytes
				dbConfig.Database,
				"-e", fmt.Sprintf("source %s", tempSQLPath))
			mysqlCmd.Stdout = os.Stdout
			mysqlCmd.Stderr = os.Stderr

			if err := mysqlCmd.Run(); err != nil {
				return fmt.Errorf("failed to import decompressed data: %v", err)
			}
		} else {
			// Use pv to show progress
			// Create a pipeline: pv file.sql | mysql
			pvCmd := exec.Command("pv",
				"-s", fmt.Sprintf("%d", fileInfo.Size()), // Set size for progress bar
				"-p", // Show percentage
				"-t", // Show elapsed time
				"-e", // Show ETA
				"-b", // Show bytes transferred
				tempSQLPath)

			mysqlCmd := exec.Command("mysql",
				"-h", dbConfig.Host,
				"-P", fmt.Sprintf("%d", dbConfig.Port),
				"-u", dbConfig.Username,
				fmt.Sprintf("-p%s", dbConfig.Password),
				"--binary-mode=1", // Add binary mode to handle null bytes
				dbConfig.Database)

			// Connect the commands in a pipeline
			pipe, err := pvCmd.StdoutPipe()
			if err != nil {
				return fmt.Errorf("failed to create pipe: %v", err)
			}
			mysqlCmd.Stdin = pipe

			// Set stderr to os.Stderr for both commands to see the progress
			pvCmd.Stderr = os.Stderr
			mysqlCmd.Stderr = os.Stderr
			mysqlCmd.Stdout = os.Stdout

			// Start the commands
			if err := pvCmd.Start(); err != nil {
				return fmt.Errorf("failed to start pv: %v", err)
			}

			if err := mysqlCmd.Start(); err != nil {
				pvCmd.Process.Kill()
				return fmt.Errorf("failed to start mysql: %v", err)
			}

			// Wait for both commands to complete
			if err := pvCmd.Wait(); err != nil {
				mysqlCmd.Process.Kill()
				return fmt.Errorf("pv command failed: %v", err)
			}

			if err := mysqlCmd.Wait(); err != nil {
				return fmt.Errorf("mysql command failed: %v", err)
			}

			log.Printf("MySQL import with progress reporting completed successfully")
		}
	} else {
		// Direct SQL file
		log.Printf("Processing SQL file directly")

		// For binary files, we need to check if it's a gzip file that wasn't detected by the extension
		fileInfo, err := os.Stat(backupFile)
		if err != nil {
			return fmt.Errorf("failed to get file info: %v", err)
		}

		// If the file is larger than 0 bytes, try to detect if it's a gzip file
		if fileInfo.Size() > 0 {
			file, err := os.Open(backupFile)
			if err != nil {
				return fmt.Errorf("failed to open file: %v", err)
			}

			// Read the first few bytes to check for gzip magic number
			header := make([]byte, 2)
			_, err = file.Read(header)
			file.Close()

			if err == nil && header[0] == 0x1f && header[1] == 0x8b {
				log.Printf("Detected gzip file by magic number, decompressing...")

				// Create a temporary file for the decompressed SQL
				tempSQLFile, err := os.CreateTemp("", fmt.Sprintf("%s-decompressed-*.sql", dbConfig.Database))
				if err != nil {
					return fmt.Errorf("failed to create temporary file for decompressed SQL: %v", err)
				}
				tempSQLPath := tempSQLFile.Name()
				tempSQLFile.Close()          // Close it so we can reopen it for writing
				defer os.Remove(tempSQLPath) // Clean up when done

				// Open the gzip file
				gzipFile, err := os.Open(backupFile)
				if err != nil {
					return fmt.Errorf("failed to open gzip file: %v", err)
				}
				defer gzipFile.Close()

				// Create a gzip reader
				gzipReader, err := gzip.NewReader(gzipFile)
				if err != nil {
					return fmt.Errorf("failed to create gzip reader: %v", err)
				}
				defer gzipReader.Close()

				// Open the output file
				outputFile, err := os.Create(tempSQLPath)
				if err != nil {
					return fmt.Errorf("failed to create output file: %v", err)
				}
				defer outputFile.Close()

				// Use buffered writer for better performance
				bufWriter := bufio.NewWriter(outputFile)

				// Start a timer for decompression
				decompressStart := time.Now()
				log.Printf("Starting decompression of gzip file")

				// Copy the decompressed data to the file with a buffer
				_, err = io.Copy(bufWriter, gzipReader)
				if err != nil {
					return fmt.Errorf("failed to decompress data: %v", err)
				}

				// Flush the buffer to ensure all data is written
				if err := bufWriter.Flush(); err != nil {
					return fmt.Errorf("failed to flush buffer: %v", err)
				}

				// Close files explicitly before running mysql
				outputFile.Close()
				gzipReader.Close()
				gzipFile.Close()

				decompressDuration := time.Since(decompressStart)
				log.Printf("Decompression completed in %s", decompressDuration)

				// Get file size for logging
				fileInfo, err := os.Stat(tempSQLPath)
				if err != nil {
					log.Printf("Warning: Could not get decompressed file size: %v", err)
				} else {
					log.Printf("Decompressed file size: %d bytes", fileInfo.Size())
				}

				// Now run mysql with the decompressed file
				// For tracker databases, we need to filter out certain statements and use additional parameters
				log.Printf("Running mysql to import the decompressed data")

				// Check if this is a tracker database that needs special handling
				isTrackerDB := strings.Contains(strings.ToLower(dbConfig.Database), "tracker")

				// Open the SQL file for reading
				sqlFile, err := os.Open(tempSQLPath)
				if err != nil {
					return fmt.Errorf("failed to open decompressed SQL file: %v", err)
				}
				defer sqlFile.Close()

				// Create the mysql command with appropriate parameters
				var mysqlCmd *exec.Cmd
				if isTrackerDB {
					log.Printf("Detected tracker database, applying special import parameters")
					mysqlCmd = exec.Command("mysql",
						"-h", dbConfig.Host,
						"-P", fmt.Sprintf("%d", dbConfig.Port),
						"-u", dbConfig.Username,
						fmt.Sprintf("-p%s", dbConfig.Password),
						"--binary-mode=1",         // Add binary mode to handle null bytes
						"--max-allowed-packet=1G", // Add max allowed packet for large imports
						dbConfig.Database)
				} else {
					mysqlCmd = exec.Command("mysql",
						"-h", dbConfig.Host,
						"-P", fmt.Sprintf("%d", dbConfig.Port),
						"-u", dbConfig.Username,
						fmt.Sprintf("-p%s", dbConfig.Password),
						"--binary-mode=1", // Add binary mode to handle null bytes
						dbConfig.Database)
				}

				// Set up pipes for streaming
				mysqlStdin, err := mysqlCmd.StdinPipe()
				if err != nil {
					return fmt.Errorf("failed to create stdin pipe: %v", err)
				}

				mysqlCmd.Stdout = os.Stdout
				mysqlCmd.Stderr = os.Stderr

				// Start the MySQL command
				log.Printf("Starting MySQL import process")
				if err := mysqlCmd.Start(); err != nil {
					return fmt.Errorf("failed to start MySQL: %v", err)
				}

				// For tracker databases, we need to filter out GTID_PURGED lines

				if isTrackerDB {
					log.Printf("Using single pipeline with pv, sed, and mysql for tracker database")

					// Close the SQL file as we'll use it differently
					sqlFile.Close()

					// Get file size for pv
					fileInfo, err = os.Stat(tempSQLPath)
					if err != nil {
						log.Printf("Warning: Could not get file size for pv: %v", err)
						// Fall back to regular import without pv
						log.Printf("Falling back to direct import without pv")
						mysqlCmd = exec.Command("mysql",
							"-h", dbConfig.Host,
							"-P", fmt.Sprintf("%d", dbConfig.Port),
							"-u", dbConfig.Username,
							fmt.Sprintf("-p%s", dbConfig.Password),
							"--binary-mode=1",         // Add binary mode to handle null bytes
							"--max-allowed-packet=1G", // Add max allowed packet for large imports
							dbConfig.Database,
							"-e", fmt.Sprintf("source %s", tempSQLPath))

						mysqlCmd.Stdout = os.Stdout
						mysqlCmd.Stderr = os.Stderr

						if err := mysqlCmd.Run(); err != nil {
							return fmt.Errorf("failed to import data: %v", err)
						}
					} else {
						// Create a pipeline: pv file.sql | sed '/SET @@GLOBAL.GTID_PURGED/d' | mysql
						log.Printf("Creating pipeline: pv | sed | mysql")

						// First command: pv with file size and forced progress output
						log.Printf("Setting up pv command with file size %d bytes", fileInfo.Size())

						// Create environment with FORCE_PROGRESS=1 to ensure pv outputs progress even in non-terminal environments
						pvEnv := append(os.Environ(), "FORCE_PROGRESS=1")

						pvCmd := exec.Command("pv",
							"-s", fmt.Sprintf("%d", fileInfo.Size()), // Set size for progress bar
							"-p", // Show percentage
							"-t", // Show elapsed time
							"-e", // Show ETA
							"-b", // Show bytes transferred
							"-f", // Force output
							tempSQLPath)

						// Set environment with FORCE_PROGRESS
						pvCmd.Env = pvEnv

						// Second command: sed to filter GTID_PURGED
						sedCmd := exec.Command("sed", "/SET @@GLOBAL.GTID_PURGED/d")

						// Third command: mysql with appropriate parameters
						mysqlCmd := exec.Command("mysql",
							"-h", dbConfig.Host,
							"-P", fmt.Sprintf("%d", dbConfig.Port),
							"-u", dbConfig.Username,
							fmt.Sprintf("-p%s", dbConfig.Password),
							"--binary-mode=1",         // Add binary mode to handle null bytes
							"--max-allowed-packet=1G", // Add max allowed packet for large imports
							dbConfig.Database)

						// Connect the commands in a pipeline
						// pv -> sed -> mysql
						pvToSedPipe, err := pvCmd.StdoutPipe()
						if err != nil {
							return fmt.Errorf("failed to create pv to sed pipe: %v", err)
						}
						sedCmd.Stdin = pvToSedPipe

						sedToMysqlPipe, err := sedCmd.StdoutPipe()
						if err != nil {
							return fmt.Errorf("failed to create sed to mysql pipe: %v", err)
						}
						mysqlCmd.Stdin = sedToMysqlPipe

						// Set stderr to os.Stderr to see the progress directly in k8s logs
						pvCmd.Stderr = os.Stderr

						// Set stderr for other commands
						sedCmd.Stderr = os.Stderr
						mysqlCmd.Stderr = os.Stderr
						mysqlCmd.Stdout = os.Stdout

						// Start the commands in reverse order
						log.Printf("Starting MySQL import pipeline (mysql -> sed -> pv)")
						log.Printf("Starting mysql command...")
						if err := mysqlCmd.Start(); err != nil {
							return fmt.Errorf("failed to start mysql: %v", err)
						}

						log.Printf("Starting sed command...")
						if err := sedCmd.Start(); err != nil {
							log.Printf("Failed to start sed, killing mysql")
							mysqlCmd.Process.Kill()
							return fmt.Errorf("failed to start sed: %v", err)
						}

						log.Printf("Starting pv command with progress reporting...")
						if err := pvCmd.Start(); err != nil {
							log.Printf("Failed to start pv, killing sed and mysql")
							sedCmd.Process.Kill()
							mysqlCmd.Process.Kill()
							return fmt.Errorf("failed to start pv: %v", err)
						}

						// PV progress will be shown directly in the k8s logs via stderr

						// Wait for all commands to complete
						log.Printf("Waiting for pv to complete...")
						if err := pvCmd.Wait(); err != nil {
							log.Printf("Warning: pv command exited with error: %v", err)
							// Don't return error here, let the pipeline continue
						} else {
							log.Printf("pv command completed successfully")
						}

						log.Printf("Waiting for sed to complete...")
						if err := sedCmd.Wait(); err != nil {
							log.Printf("Warning: sed command exited with error: %v", err)
							// Don't return error here, let the pipeline continue
						} else {
							log.Printf("sed command completed successfully")
						}

						log.Printf("Waiting for mysql to complete...")
						if err := mysqlCmd.Wait(); err != nil {
							return fmt.Errorf("mysql command failed: %v", err)
						} else {
							log.Printf("mysql command completed successfully")
						}

						// PV output has been shown directly in the k8s logs

						log.Printf("MySQL import pipeline completed successfully")
					}
				} else {
					// For non-tracker databases, use pv directly with the SQL file
					log.Printf("Using pv with SQL file for non-tracker database")

					// Get file size for pv
					fileInfo, err = os.Stat(tempSQLPath)
					if err != nil {
						log.Printf("Warning: Could not get file size for pv: %v", err)
						// Fall back to regular import without pv
						mysqlCmd.Stdout = os.Stdout
						mysqlCmd.Stderr = os.Stderr

						// Create a goroutine to stream the SQL file to MySQL
						go func() {
							defer mysqlStdin.Close() // Make sure to close the pipe when done

							reader := bufio.NewReader(sqlFile)
							writer := bufio.NewWriter(mysqlStdin)

							// Simple copy without filtering
							_, err := io.Copy(writer, reader)
							if err != nil {
								log.Printf("Error copying SQL file to MySQL: %v", err)
								return
							}

							// Final flush to ensure all data is written
							writer.Flush()
							log.Printf("Finished streaming SQL file to MySQL")
						}()

						// Wait for MySQL to finish processing
						if err := mysqlCmd.Wait(); err != nil {
							return fmt.Errorf("MySQL import failed: %v", err)
						}
					} else {
						// Close the current file and stdin pipe
						sqlFile.Close()
						mysqlStdin.Close()

						// Use pv to show progress
						pvCmd := exec.Command("pv",
							"-s", fmt.Sprintf("%d", fileInfo.Size()), // Set size for progress bar
							"-p", // Show percentage
							"-t", // Show elapsed time
							"-e", // Show ETA
							"-b", // Show bytes transferred
							tempSQLPath)

						newMysqlCmd := exec.Command("mysql",
							"-h", dbConfig.Host,
							"-P", fmt.Sprintf("%d", dbConfig.Port),
							"-u", dbConfig.Username,
							fmt.Sprintf("-p%s", dbConfig.Password),
							"--binary-mode=1", // Add binary mode to handle null bytes
							dbConfig.Database)

						// Connect the commands in a pipeline
						pipe, err := pvCmd.StdoutPipe()
						if err != nil {
							return fmt.Errorf("failed to create pipe: %v", err)
						}
						newMysqlCmd.Stdin = pipe

						// Set stderr to os.Stderr for both commands to see the progress
						pvCmd.Stderr = os.Stderr
						newMysqlCmd.Stderr = os.Stderr
						newMysqlCmd.Stdout = os.Stdout

						// Start the commands
						if err := pvCmd.Start(); err != nil {
							return fmt.Errorf("failed to start pv: %v", err)
						}

						if err := newMysqlCmd.Start(); err != nil {
							pvCmd.Process.Kill()
							return fmt.Errorf("failed to start mysql: %v", err)
						}

						// Wait for both commands to complete
						if err := pvCmd.Wait(); err != nil {
							newMysqlCmd.Process.Kill()
							return fmt.Errorf("pv command failed: %v", err)
						}

						if err := newMysqlCmd.Wait(); err != nil {
							return fmt.Errorf("mysql command failed: %v", err)
						}
					}
				}

				log.Printf("MySQL import with progress reporting completed successfully")

				return nil
			}
		}

		// If we get here, it's a regular SQL file
		log.Printf("Using pv to import SQL file with progress reporting")

		// Get file size for pv
		fileInfo, err = os.Stat(backupFile)
		if err != nil {
			log.Printf("Warning: Could not get file size for pv: %v", err)
			// Fall back to regular import without pv
			cmd = exec.Command("mysql",
				"-h", dbConfig.Host,
				"-P", fmt.Sprintf("%d", dbConfig.Port),
				"-u", dbConfig.Username,
				fmt.Sprintf("-p%s", dbConfig.Password),
				"--binary-mode=1", // Add binary mode to handle null bytes
				dbConfig.Database,
				"-e", fmt.Sprintf("source %s", backupFile))

			cmd.Stdout = os.Stdout
			cmd.Stderr = os.Stderr

			if err := cmd.Run(); err != nil {
				return fmt.Errorf("failed to restore database: %v", err)
			}
		} else {
			// Use pv to show progress
			// Create a pipeline: pv file.sql | mysql
			pvCmd := exec.Command("pv",
				"-s", fmt.Sprintf("%d", fileInfo.Size()), // Set size for progress bar
				"-p", // Show percentage
				"-t", // Show elapsed time
				"-e", // Show ETA
				"-b", // Show bytes transferred
				backupFile)

			mysqlCmd := exec.Command("mysql",
				"-h", dbConfig.Host,
				"-P", fmt.Sprintf("%d", dbConfig.Port),
				"-u", dbConfig.Username,
				fmt.Sprintf("-p%s", dbConfig.Password),
				"--binary-mode=1", // Add binary mode to handle null bytes
				dbConfig.Database)

			// Connect the commands in a pipeline
			pipe, err := pvCmd.StdoutPipe()
			if err != nil {
				return fmt.Errorf("failed to create pipe: %v", err)
			}
			mysqlCmd.Stdin = pipe

			// Set stderr to os.Stderr for both commands to see the progress
			pvCmd.Stderr = os.Stderr
			mysqlCmd.Stderr = os.Stderr
			mysqlCmd.Stdout = os.Stdout

			// Start the commands
			if err := pvCmd.Start(); err != nil {
				return fmt.Errorf("failed to start pv: %v", err)
			}

			if err := mysqlCmd.Start(); err != nil {
				pvCmd.Process.Kill()
				return fmt.Errorf("failed to start mysql: %v", err)
			}

			// Wait for both commands to complete
			if err := pvCmd.Wait(); err != nil {
				mysqlCmd.Process.Kill()
				return fmt.Errorf("pv command failed: %v", err)
			}

			if err := mysqlCmd.Wait(); err != nil {
				return fmt.Errorf("mysql command failed: %v", err)
			}

			log.Printf("MySQL import with progress reporting completed successfully")
		}
	}

	// Calculate and log the time taken
	duration := time.Since(startTime)
	log.Printf("MySQL database %s restored successfully in %s", dbConfig.Database, duration)
	return nil
}

// MySQLDump creates a dump of a MySQL database
func MySQLDump(dbConfig config.RestoreDatabaseConfig) (string, error) {
	log.Printf("Creating MySQL dump for database %s", dbConfig.Database)

	// Create a temporary file to store the dump
	tempFile, err := os.CreateTemp("", fmt.Sprintf("%s-sanitized-*.sql.gz", dbConfig.Database))
	if err != nil {
		return "", fmt.Errorf("failed to create temporary file for sanitized dump: %v", err)
	}
	tempFile.Close() // Close the file so the pipeline can write to it

	// Use mysqldump and gzip to create the sanitized dump
	mysqldumpCmd := exec.Command("mysqldump",
		"-h", dbConfig.Host,
		"-P", fmt.Sprintf("%d", dbConfig.Port),
		"-u", dbConfig.Username,
		fmt.Sprintf("-p%s", dbConfig.Password),
		dbConfig.Database)

	gzipCmd := exec.Command("gzip")

	// Set up the pipeline: mysqldump | gzip > tempFile
	gzipInput, _ := mysqldumpCmd.StdoutPipe()
	gzipCmd.Stdin = gzipInput

	gzipOutput, _ := os.Create(tempFile.Name())
	gzipCmd.Stdout = gzipOutput

	// Start the commands
	if err := gzipCmd.Start(); err != nil {
		return "", fmt.Errorf("failed to start gzip: %v", err)
	}

	if err := mysqldumpCmd.Run(); err != nil {
		gzipCmd.Process.Kill()
		return "", fmt.Errorf("failed to run mysqldump: %v", err)
	}

	if err := gzipCmd.Wait(); err != nil {
		return "", fmt.Errorf("failed to complete gzip: %v", err)
	}

	gzipOutput.Close()

	log.Printf("MySQL dump created successfully: %s", tempFile.Name())
	return tempFile.Name(), nil
}
