package k8s

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/util/retry"
)

// DeploymentManager handles scaling Kubernetes deployments
type DeploymentManager struct {
	clientset *kubernetes.Clientset
	namespace string
}

// NewDeploymentManager creates a new DeploymentManager
func NewDeploymentManager(namespace string) (*DeploymentManager, error) {
	// Create in-cluster config
	config, err := rest.InClusterConfig()
	if err != nil {
		return nil, fmt.Errorf("failed to create in-cluster config: %v", err)
	}

	// Create clientset
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create clientset: %v", err)
	}

	return &DeploymentManager{
		clientset: clientset,
		namespace: namespace,
	}, nil
}

// ScaleDeployment scales a deployment to the specified number of replicas
func (dm *DeploymentManager) ScaleDeployment(deploymentName string, replicas int32) error {
	deploymentsClient := dm.clientset.AppsV1().Deployments(dm.namespace)

	// Use RetryOnConflict to handle potential conflicts when updating the deployment
	return retry.RetryOnConflict(retry.DefaultRetry, func() error {
		// Get the latest version of the deployment
		deployment, err := deploymentsClient.Get(context.TODO(), deploymentName, metav1.GetOptions{})
		if err != nil {
			return fmt.Errorf("failed to get deployment %s: %v", deploymentName, err)
		}

		// Check if the deployment already has the desired number of replicas
		if *deployment.Spec.Replicas == replicas {
			log.Printf("Deployment %s already has %d replicas", deploymentName, replicas)
			return nil
		}

		// Update the deployment with the new replica count
		deployment.Spec.Replicas = &replicas
		_, err = deploymentsClient.Update(context.TODO(), deployment, metav1.UpdateOptions{})
		if err != nil {
			return fmt.Errorf("failed to update deployment %s: %v", deploymentName, err)
		}

		log.Printf("Successfully scaled deployment %s to %d replicas", deploymentName, replicas)
		return nil
	})
}

// GetDeploymentReplicas gets the current number of replicas for a deployment
func (dm *DeploymentManager) GetDeploymentReplicas(deploymentName string) (int32, error) {
	deploymentsClient := dm.clientset.AppsV1().Deployments(dm.namespace)

	// Get the deployment
	deployment, err := deploymentsClient.Get(context.TODO(), deploymentName, metav1.GetOptions{})
	if err != nil {
		return 0, fmt.Errorf("failed to get deployment %s: %v", deploymentName, err)
	}

	return *deployment.Spec.Replicas, nil
}

// WaitForDeploymentReady waits for a deployment to be ready
func (dm *DeploymentManager) WaitForDeploymentReady(deploymentName string, timeout time.Duration) error {
	deploymentsClient := dm.clientset.AppsV1().Deployments(dm.namespace)

	// Start time for timeout calculation
	startTime := time.Now()

	for {
		// Check if timeout has been reached
		if time.Since(startTime) > timeout {
			return fmt.Errorf("timeout waiting for deployment %s to be ready", deploymentName)
		}

		// Get the deployment
		deployment, err := deploymentsClient.Get(context.TODO(), deploymentName, metav1.GetOptions{})
		if err != nil {
			return fmt.Errorf("failed to get deployment %s: %v", deploymentName, err)
		}

		// Check if the deployment is ready
		if deployment.Status.ReadyReplicas == *deployment.Spec.Replicas &&
			deployment.Status.Replicas == *deployment.Spec.Replicas &&
			deployment.Status.AvailableReplicas == *deployment.Spec.Replicas {
			log.Printf("Deployment %s is ready with %d replicas", deploymentName, *deployment.Spec.Replicas)
			return nil
		}

		log.Printf("Waiting for deployment %s to be ready (current: %d/%d ready, %d/%d available)",
			deploymentName,
			deployment.Status.ReadyReplicas,
			*deployment.Spec.Replicas,
			deployment.Status.AvailableReplicas,
			*deployment.Spec.Replicas)

		// Wait before checking again
		time.Sleep(5 * time.Second)
	}
}

// DeploymentExists checks if a deployment exists
func (dm *DeploymentManager) DeploymentExists(deploymentName string) (bool, error) {
	deploymentsClient := dm.clientset.AppsV1().Deployments(dm.namespace)

	_, err := deploymentsClient.Get(context.TODO(), deploymentName, metav1.GetOptions{})
	if err != nil {
		// Check if the error is a "not found" error
		if strings.Contains(err.Error(), "not found") {
			return false, nil
		}
		return false, fmt.Errorf("failed to check if deployment %s exists: %v", deploymentName, err)
	}

	return true, nil
}
