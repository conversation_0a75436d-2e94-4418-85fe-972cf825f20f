package k8s

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
)

// DatabaseType represents the type of database
type DatabaseType string

const (
	// MySQL database type
	MySQL DatabaseType = "mysql"
	// PostgreSQL database type
	PostgreSQL DatabaseType = "postgres"
)

// DatabaseService manages database deployments
type DatabaseService struct {
	deploymentManager  *DeploymentManager
	namespace          string
	mysqlDeployment    string
	postgresDeployment string
	scaleUpTime        time.Time
	minUptimeMinutes   int
	acquiredLocks      map[DatabaseType]bool // Track which locks are acquired
}

// NewDatabaseService creates a new DatabaseService
func NewDatabaseService() (*DatabaseService, error) {
	namespace := os.Getenv("POD_NAMESPACE")
	if namespace == "" {
		namespace = "default"
	}

	deploymentManager, err := NewDeploymentManager(namespace)
	if err != nil {
		return nil, fmt.Errorf("failed to create deployment manager: %v", err)
	}

	return &DatabaseService{
		deploymentManager:  deploymentManager,
		namespace:          namespace,
		mysqlDeployment:    "mysql",
		postgresDeployment: "postgres",
		scaleUpTime:        time.Time{}, // Zero time (not set yet)
		minUptimeMinutes:   10,          // 10 minutes minimum uptime
		acquiredLocks:      make(map[DatabaseType]bool),
	}, nil
}

// getLockName returns the lock name for a specific database type
func (ds *DatabaseService) getLockName(dbType DatabaseType) string {
	return fmt.Sprintf("db-scaling-lock-%s", string(dbType))
}

// ScaleUpDatabase scales up a database deployment
func (ds *DatabaseService) ScaleUpDatabase(dbType DatabaseType) error {
	var deploymentName string
	switch dbType {
	case MySQL:
		deploymentName = ds.mysqlDeployment
	case PostgreSQL:
		deploymentName = ds.postgresDeployment
	default:
		return fmt.Errorf("unsupported database type: %s", dbType)
	}

	// Check if deployment exists
	exists, err := ds.deploymentManager.DeploymentExists(deploymentName)
	if err != nil {
		return fmt.Errorf("failed to check if deployment %s exists: %v", deploymentName, err)
	}
	if !exists {
		return fmt.Errorf("deployment %s does not exist", deploymentName)
	}

	// Get current replicas
	currentReplicas, err := ds.deploymentManager.GetDeploymentReplicas(deploymentName)
	if err != nil {
		return fmt.Errorf("failed to get current replicas for deployment %s: %v", deploymentName, err)
	}

	// If already scaled up, nothing to do
	if currentReplicas > 0 {
		log.Printf("Deployment %s is already scaled up with %d replicas", deploymentName, currentReplicas)
		return nil
	}

	// Scale up to 1 replica
	log.Printf("Scaling up deployment %s from %d to 1 replica", deploymentName, currentReplicas)
	if err := ds.deploymentManager.ScaleDeployment(deploymentName, 1); err != nil {
		return fmt.Errorf("failed to scale up deployment %s: %v", deploymentName, err)
	}

	// Wait for deployment to be ready
	log.Printf("Waiting for deployment %s to be ready", deploymentName)
	if err := ds.deploymentManager.WaitForDeploymentReady(deploymentName, 5*time.Minute); err != nil {
		return fmt.Errorf("failed to wait for deployment %s to be ready: %v", deploymentName, err)
	}

	return nil
}

// ScaleDownDatabase scales down a database deployment
func (ds *DatabaseService) ScaleDownDatabase(dbType DatabaseType) error {
	var deploymentName string
	switch dbType {
	case MySQL:
		deploymentName = ds.mysqlDeployment
	case PostgreSQL:
		deploymentName = ds.postgresDeployment
	default:
		return fmt.Errorf("unsupported database type: %s", dbType)
	}

	// Check if deployment exists
	exists, err := ds.deploymentManager.DeploymentExists(deploymentName)
	if err != nil {
		return fmt.Errorf("failed to check if deployment %s exists: %v", deploymentName, err)
	}
	if !exists {
		return fmt.Errorf("deployment %s does not exist", deploymentName)
	}

	// Get current replicas
	currentReplicas, err := ds.deploymentManager.GetDeploymentReplicas(deploymentName)
	if err != nil {
		return fmt.Errorf("failed to get current replicas for deployment %s: %v", deploymentName, err)
	}

	// If already scaled down, nothing to do
	if currentReplicas == 0 {
		log.Printf("Deployment %s is already scaled down with 0 replicas", deploymentName)
		return nil
	}

	// Scale down to 0 replicas
	log.Printf("Scaling down deployment %s from %d to 0 replicas", deploymentName, currentReplicas)
	if err := ds.deploymentManager.ScaleDeployment(deploymentName, 0); err != nil {
		return fmt.Errorf("failed to scale down deployment %s: %v", deploymentName, err)
	}

	// Verify the deployment was actually scaled down
	verifyReplicas, err := ds.deploymentManager.GetDeploymentReplicas(deploymentName)
	if err != nil {
		log.Printf("Warning: Could not verify replica count after scaling down %s: %v", deploymentName, err)
	} else if verifyReplicas != 0 {
		log.Printf("Warning: Deployment %s was not scaled down correctly. Current replicas: %d", deploymentName, verifyReplicas)
		return fmt.Errorf("deployment %s still has %d replicas after scale down", deploymentName, verifyReplicas)
	} else {
		log.Printf("Successfully scaled down deployment %s to 0 replicas", deploymentName)
	}

	return nil
}

// EnsureDatabasesRunning ensures that both MySQL and PostgreSQL databases are running
func (ds *DatabaseService) EnsureDatabasesRunning() error {
	return ds.EnsureSelectiveDatabasesRunning([]DatabaseType{MySQL, PostgreSQL})
}

// EnsureSelectiveDatabasesRunning ensures that only the specified database types are running
func (ds *DatabaseService) EnsureSelectiveDatabasesRunning(dbTypes []DatabaseType) error {
	if len(dbTypes) == 0 {
		log.Println("No database types specified, skipping database scaling")
		return nil
	}

	// Acquire locks for each database type to prevent conflicts with other sanitization jobs
	for _, dbType := range dbTypes {
		if err := ds.acquireLockForType(dbType); err != nil {
			// If we fail to acquire a lock, release any locks we've already acquired
			ds.releaseAcquiredLocks()
			return fmt.Errorf("failed to acquire database scaling lock for %s: %v", dbType, err)
		}
	}

	log.Printf("Scaling up databases for types: %v", dbTypes)

	for _, dbType := range dbTypes {
		if err := ds.ScaleUpDatabase(dbType); err != nil {
			log.Printf("Warning: Failed to scale up %s: %v", dbType, err)
		}
	}

	// Record the time when databases were scaled up
	ds.scaleUpTime = time.Now()
	log.Printf("Databases scaled up at %s", ds.scaleUpTime.Format(time.RFC3339))

	return nil
}

// ShutdownDatabases shuts down both MySQL and PostgreSQL databases
// but only if minimum uptime has been reached AND no db-sanitize jobs are running
func (ds *DatabaseService) ShutdownDatabases() error {
	return ds.ShutdownSelectiveDatabases([]DatabaseType{MySQL, PostgreSQL})
}

// ShutdownSelectiveDatabases shuts down only the specified database types
// but only if minimum uptime has been reached AND no db-sanitize jobs are running
func (ds *DatabaseService) ShutdownSelectiveDatabases(dbTypes []DatabaseType) error {
	if len(dbTypes) == 0 {
		log.Println("No database types specified, skipping database shutdown")
		return nil
	}

	// Check if minimum uptime has been reached
	if !ds.scaleUpTime.IsZero() {
		elapsedMinutes := time.Since(ds.scaleUpTime).Minutes()
		if elapsedMinutes < float64(ds.minUptimeMinutes) {
			remainingMinutes := float64(ds.minUptimeMinutes) - elapsedMinutes
			log.Printf("Minimum uptime of %d minutes not reached. Databases have been up for %.1f minutes. Waiting %.1f more minutes before scaling down.",
				ds.minUptimeMinutes, elapsedMinutes, remainingMinutes)

			// Wait for the remaining time
			time.Sleep(time.Duration(remainingMinutes) * time.Minute)
			log.Printf("Minimum uptime reached. Proceeding to check for active jobs.")
		} else {
			log.Printf("Minimum uptime of %d minutes reached. Databases have been up for %.1f minutes.",
				ds.minUptimeMinutes, elapsedMinutes)
		}
	} else {
		log.Printf("No scale-up time recorded. Proceeding to check for active jobs.")
	}

	// Check for active db-sanitize jobs
	if hasActiveDBSanitizeJobs() {
		log.Printf("Active db-sanitize jobs detected. Skipping database scale down to avoid disrupting running jobs.")
		return nil
	}

	log.Printf("No active db-sanitize jobs detected. Proceeding with database shutdown for types: %v", dbTypes)

	var errors []error
	for _, dbType := range dbTypes {
		if err := ds.ScaleDownDatabase(dbType); err != nil {
			log.Printf("Warning: Failed to scale down %s: %v", dbType, err)
			errors = append(errors, err)
		}
	}

	// Release the locks for the database types that were scaled down
	for _, dbType := range dbTypes {
		if err := ds.releaseLockForType(dbType); err != nil {
			log.Printf("Warning: Failed to release database scaling lock for %s: %v", dbType, err)
		}
	}

	// Return an error if any scale-down operations failed
	if len(errors) > 0 {
		return fmt.Errorf("failed to scale down some databases: %v", errors)
	}

	return nil
}

// hasActiveDBSanitizeJobs checks if there are any active db-sanitize jobs in the cluster
// This is called only once during the shutdown process, not periodically
func hasActiveDBSanitizeJobs() bool {
	// Try to initialize Kubernetes client if we're running in a cluster
	var k8sClient *kubernetes.Clientset
	if os.Getenv("KUBERNETES_SERVICE_HOST") != "" {
		config, err := rest.InClusterConfig()
		if err == nil {
			client, err := kubernetes.NewForConfig(config)
			if err == nil {
				k8sClient = client
			} else {
				log.Printf("Warning: Failed to create Kubernetes client: %v", err)
				return false // Assume no active jobs if we can't check
			}
		} else {
			log.Printf("Warning: Failed to get in-cluster config: %v", err)
			return false // Assume no active jobs if we can't check
		}
	} else {
		log.Printf("Not running in Kubernetes, skipping job check")
		return false
	}

	namespace := os.Getenv("POD_NAMESPACE")
	if namespace == "" {
		namespace = "default"
	}

	// Get all jobs in the namespace
	jobs, err := k8sClient.BatchV1().Jobs(namespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		log.Printf("Warning: Failed to list jobs: %v", err)
		return false // Assume no active jobs if we can't check
	}

	// Check for active or pending db-sanitize jobs
	for _, job := range jobs.Items {
		if strings.HasPrefix(job.Name, "db-sanitize") {
			// Consider a job active if it's not completed or failed
			if job.Status.Active > 0 || (job.Status.Succeeded == 0 && job.Status.Failed == 0) {
				log.Printf("Found active db-sanitize job: %s (active: %d, succeeded: %d, failed: %d)",
					job.Name, job.Status.Active, job.Status.Succeeded, job.Status.Failed)
				return true
			}
		}
	}

	log.Printf("No active db-sanitize jobs found. Safe to scale down databases.")
	return false
}

// acquireLockForType acquires a lock for a specific database type to prevent conflicts between multiple sanitization jobs
func (ds *DatabaseService) acquireLockForType(dbType DatabaseType) error {
	// Try to initialize Kubernetes client if we're running in a cluster
	var k8sClient *kubernetes.Clientset
	if os.Getenv("KUBERNETES_SERVICE_HOST") != "" {
		config, err := rest.InClusterConfig()
		if err != nil {
			return fmt.Errorf("failed to get in-cluster config: %v", err)
		}
		client, err := kubernetes.NewForConfig(config)
		if err != nil {
			return fmt.Errorf("failed to create Kubernetes client: %v", err)
		}
		k8sClient = client
	} else {
		log.Printf("Not running in Kubernetes, skipping lock acquisition for %s", dbType)
		return nil
	}

	lockName := ds.getLockName(dbType)

	// Create a ConfigMap to act as a lock
	lockConfigMap := &metav1.ObjectMeta{
		Name:      lockName,
		Namespace: ds.namespace,
		Labels: map[string]string{
			"app":     "mneme",
			"type":    "database-scaling-lock",
			"db-type": string(dbType),
		},
	}

	// Try to create the ConfigMap (this will fail if it already exists)
	_, err := k8sClient.CoreV1().ConfigMaps(ds.namespace).Create(context.TODO(), &v1.ConfigMap{
		ObjectMeta: *lockConfigMap,
		Data: map[string]string{
			"locked-by": os.Getenv("HOSTNAME"),
			"locked-at": time.Now().Format(time.RFC3339),
			"db-type":   string(dbType),
		},
	}, metav1.CreateOptions{})

	if err != nil {
		// If the ConfigMap already exists, another job has the lock
		if strings.Contains(err.Error(), "already exists") {
			return fmt.Errorf("database scaling lock for %s is already held by another job", dbType)
		}
		return fmt.Errorf("failed to create lock ConfigMap for %s: %v", dbType, err)
	}

	// Mark this lock as acquired
	ds.acquiredLocks[dbType] = true
	log.Printf("Successfully acquired database scaling lock for %s", dbType)
	return nil
}

// releaseLockForType releases the database scaling lock for a specific database type
func (ds *DatabaseService) releaseLockForType(dbType DatabaseType) error {
	// Check if we actually acquired this lock
	if !ds.acquiredLocks[dbType] {
		log.Printf("Lock for %s was not acquired by this service, skipping release", dbType)
		return nil
	}

	// Try to initialize Kubernetes client if we're running in a cluster
	var k8sClient *kubernetes.Clientset
	if os.Getenv("KUBERNETES_SERVICE_HOST") != "" {
		config, err := rest.InClusterConfig()
		if err != nil {
			return fmt.Errorf("failed to get in-cluster config: %v", err)
		}
		client, err := kubernetes.NewForConfig(config)
		if err != nil {
			return fmt.Errorf("failed to create Kubernetes client: %v", err)
		}
		k8sClient = client
	} else {
		log.Printf("Not running in Kubernetes, skipping lock release for %s", dbType)
		delete(ds.acquiredLocks, dbType)
		return nil
	}

	lockName := ds.getLockName(dbType)

	// Delete the ConfigMap lock
	err := k8sClient.CoreV1().ConfigMaps(ds.namespace).Delete(context.TODO(), lockName, metav1.DeleteOptions{})
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			log.Printf("Lock ConfigMap for %s was already deleted", dbType)
		} else {
			return fmt.Errorf("failed to delete lock ConfigMap for %s: %v", dbType, err)
		}
	} else {
		log.Printf("Successfully released database scaling lock for %s", dbType)
	}

	// Remove from acquired locks
	delete(ds.acquiredLocks, dbType)
	return nil
}

// releaseAcquiredLocks releases all locks that were acquired by this service
func (ds *DatabaseService) releaseAcquiredLocks() {
	for dbType := range ds.acquiredLocks {
		if err := ds.releaseLockForType(dbType); err != nil {
			log.Printf("Warning: Failed to release lock for %s: %v", dbType, err)
		}
	}
}

// AnalyzeRequiredDatabaseTypes analyzes a list of database configurations and returns the unique database types needed
func AnalyzeRequiredDatabaseTypes(configs interface{}) []DatabaseType {
	var dbTypes []DatabaseType
	typeSet := make(map[DatabaseType]bool)

	// Handle different config types
	switch cfgs := configs.(type) {
	case []interface{}:
		// Handle generic interface slice (from JSON unmarshaling)
		for _, cfg := range cfgs {
			if configMap, ok := cfg.(map[string]interface{}); ok {
				if enabled, hasEnabled := configMap["enabled"]; hasEnabled {
					if enabledBool, ok := enabled.(bool); ok && !enabledBool {
						continue // Skip disabled databases
					}
				}
				if dbType, ok := configMap["type"].(string); ok {
					switch dbType {
					case "mysql":
						if !typeSet[MySQL] {
							dbTypes = append(dbTypes, MySQL)
							typeSet[MySQL] = true
						}
					case "postgresql":
						if !typeSet[PostgreSQL] {
							dbTypes = append(dbTypes, PostgreSQL)
							typeSet[PostgreSQL] = true
						}
					}
				}
			}
		}
	default:
		log.Printf("Warning: Unsupported config type for database analysis: %T", configs)
	}

	log.Printf("Analyzed database configurations, required types: %v", dbTypes)
	return dbTypes
}
