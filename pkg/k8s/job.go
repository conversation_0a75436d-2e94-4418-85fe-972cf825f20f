package k8s

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/exec"
	"strings"
	"time"

	batchv1 "k8s.io/api/batch/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
)

// WaitForDBSanitizeJobs waits for all db-sanitize jobs to complete
// with a hard timeout of 180 minutes
func WaitForDBSanitizeJobs(clientset *kubernetes.Clientset, namespace string, timeout time.Duration) error {
	// Hard timeout of 180 minutes for db-sanitize jobs
	hardTimeout := 180 * time.Minute
	return waitForJobsWithPrefix(clientset, namespace, "db-sanitize", timeout, hardTimeout)
}

// waitForJobsWithPrefix waits for all jobs with the given prefix to complete
func waitForJobsWithPrefix(clientset *kubernetes.Clientset, namespace, prefix string, timeout time.Duration, hardTimeout ...time.Duration) error {
	log.Printf("Waiting for jobs with prefix '%s' to complete...", prefix)

	// Start time for timeout calculation
	startTime := time.Now()

	// Flag to track if we've seen any jobs with this prefix
	jobsFound := false

	// Initial delay to allow jobs to be created and start
	initialDelay := 30 * time.Second
	log.Printf("Waiting %s for jobs with prefix '%s' to start...", initialDelay, prefix)
	time.Sleep(initialDelay)

	// Check if a hard timeout was provided
	var hardTimeoutValue time.Duration
	if len(hardTimeout) > 0 && hardTimeout[0] > 0 {
		hardTimeoutValue = hardTimeout[0]
		log.Printf("Hard timeout of %s set for jobs with prefix '%s'", hardTimeoutValue, prefix)
	}

	for {
		// Check if hard timeout has been reached for db-sanitize jobs
		if prefix == "db-sanitize" && hardTimeoutValue > 0 && time.Since(startTime) > hardTimeoutValue {
			log.Printf("HARD TIMEOUT of %s reached for '%s' jobs. Killing all jobs and cleaning up.", hardTimeoutValue, prefix)

			// Kill all jobs with this prefix
			if err := DeleteJobsWithPrefix(clientset, namespace, prefix); err != nil {
				log.Printf("Warning: Failed to delete jobs with prefix '%s': %v", prefix, err)
			}

			// Drop all temporary databases
			if err := DropTemporaryDatabases(); err != nil {
				log.Printf("Warning: Failed to drop temporary databases: %v", err)
			}

			// Return an error to indicate the hard timeout was reached
			return fmt.Errorf("hard timeout of %s reached for jobs with prefix '%s'", hardTimeoutValue, prefix)
		}

		// Check if regular timeout has been reached
		if time.Since(startTime) > timeout {
			if !jobsFound {
				log.Printf("Warning: No jobs with prefix '%s' were found within timeout period", prefix)
				return nil // Return success if no jobs were found (nothing to wait for)
			}

			// For db-sanitize jobs, we want to ensure they complete even if it takes longer than the timeout
			// This is critical because databases must remain up until all sanitization jobs are done
			if prefix == "db-sanitize" {
				log.Printf("Timeout reached, but continuing to wait for '%s' jobs to complete as they are critical", prefix)
				// Continue waiting by not returning an error
			} else {
				// For other job types, respect the timeout
				return fmt.Errorf("timeout waiting for jobs with prefix '%s' to complete", prefix)
			}
		}

		// Get all jobs in the namespace
		jobs, err := clientset.BatchV1().Jobs(namespace).List(context.TODO(), metav1.ListOptions{})
		if err != nil {
			return fmt.Errorf("failed to list jobs: %v", err)
		}

		// Filter jobs with the given prefix
		var activeJobs []string
		var completedJobs []string
		var failedJobs []string

		for _, job := range jobs.Items {
			if strings.HasPrefix(job.Name, prefix) {
				jobsFound = true

				// Check job status
				if job.Status.Active > 0 {
					activeJobs = append(activeJobs, job.Name)
				} else if job.Status.Succeeded > 0 {
					completedJobs = append(completedJobs, job.Name)
				} else if job.Status.Failed > 0 {
					failedJobs = append(failedJobs, job.Name)
				} else {
					// Job exists but hasn't started yet
					activeJobs = append(activeJobs, job.Name+" (pending)")
				}
			}
		}

		// If no active jobs and we've found at least one job, we're done
		if len(activeJobs) == 0 {
			if jobsFound {
				if len(failedJobs) > 0 {
					log.Printf("Warning: Some jobs with prefix '%s' have failed: %v", prefix, failedJobs)

					// For db-sanitize jobs, drop the temporary databases when jobs fail
					if prefix == "db-sanitize" {
						log.Printf("Cleaning up temporary databases for failed db-sanitize jobs")
						for _, jobName := range failedJobs {
							// Extract database name from job name (format: db-sanitize-{sanitized-dbname}-{timestamp})
							parts := strings.Split(jobName, "-")
							if len(parts) >= 3 {
								// The database name could contain multiple hyphens, so we need to join all parts except the first two and the last one
								// First two parts are "db" and "sanitize", and the last part is the timestamp
								dbNameParts := parts[2 : len(parts)-1]
								sanitizedDbName := strings.Join(dbNameParts, "-")
								// Convert back to original database name (replace hyphens with underscores)
								dbName := unsanitizeJobName(sanitizedDbName)

								log.Printf("Dropping temporary database for failed job %s, sanitized name: %s, original name: %s", jobName, sanitizedDbName, dbName)
								if err := DropTemporaryDatabaseByName(dbName); err != nil {
									log.Printf("Warning: Failed to drop temporary database %s: %v", dbName, err)
								}
							} else {
								log.Printf("Warning: Could not extract database name from job name: %s", jobName)
							}
						}
					}
				}
				log.Printf("All jobs with prefix '%s' have completed. Succeeded: %d, Failed: %d",
					prefix, len(completedJobs), len(failedJobs))
				return nil
			} else {
				// No jobs found yet, keep waiting
				log.Printf("No jobs with prefix '%s' found yet. Continuing to wait...", prefix)
			}
		} else {
			// Log active jobs
			log.Printf("Waiting for %d jobs with prefix '%s' to complete. Active: %v, Completed: %d, Failed: %d",
				len(activeJobs), prefix, activeJobs, len(completedJobs), len(failedJobs))
		}

		// Wait before checking again (check once per minute)
		time.Sleep(1 * time.Minute)
	}
}

// DeleteJobsWithPrefix deletes all jobs with the given prefix in the namespace
func DeleteJobsWithPrefix(clientset *kubernetes.Clientset, namespace, prefix string) error {
	log.Printf("Deleting all jobs with prefix '%s' in namespace '%s'", prefix, namespace)

	// Get all jobs in the namespace
	jobs, err := clientset.BatchV1().Jobs(namespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		return fmt.Errorf("failed to list jobs: %v", err)
	}

	// Delete jobs with the given prefix
	deletedCount := 0
	for _, job := range jobs.Items {
		if strings.HasPrefix(job.Name, prefix) {
			log.Printf("Deleting job: %s", job.Name)

			// Delete the job
			deletePolicy := metav1.DeletePropagationForeground
			if err := clientset.BatchV1().Jobs(namespace).Delete(context.TODO(), job.Name, metav1.DeleteOptions{
				PropagationPolicy: &deletePolicy,
			}); err != nil {
				log.Printf("Warning: Failed to delete job %s: %v", job.Name, err)
			} else {
				deletedCount++
			}
		}
	}

	log.Printf("Deleted %d jobs with prefix '%s'", deletedCount, prefix)
	return nil
}

// DropTemporaryDatabases drops all temporary databases on MySQL and PostgreSQL pods
func DropTemporaryDatabases() error {
	log.Printf("Dropping all temporary databases on MySQL and PostgreSQL pods")

	// Drop MySQL temporary databases
	if err := dropMySQLTemporaryDatabases(); err != nil {
		log.Printf("Warning: Failed to drop MySQL temporary databases: %v", err)
	}

	// Drop PostgreSQL temporary databases
	if err := dropPostgreSQLTemporaryDatabases(); err != nil {
		log.Printf("Warning: Failed to drop PostgreSQL temporary databases: %v", err)
	}

	return nil
}

// dropMySQLTemporaryDatabases drops all temporary databases on MySQL pods
func dropMySQLTemporaryDatabases() error {
	log.Printf("Dropping temporary MySQL databases")

	// Get MySQL root credentials from environment variables
	mysqlRootUsername := os.Getenv("MYSQL_ROOT_USERNAME")
	if mysqlRootUsername == "" {
		mysqlRootUsername = "root"
	}

	mysqlRootPassword := os.Getenv("MYSQL_ROOT_PASSWORD")
	if mysqlRootPassword == "" {
		log.Printf("Warning: MYSQL_ROOT_PASSWORD environment variable not set")
		return fmt.Errorf("MYSQL_ROOT_PASSWORD environment variable not set")
	}

	// Get MySQL host from environment variable
	mysqlHost := os.Getenv("MYSQL_HOST")
	if mysqlHost == "" {
		mysqlHost = "mysql"
	}

	// Get MySQL port from environment variable
	mysqlPort := os.Getenv("MYSQL_PORT")
	if mysqlPort == "" {
		mysqlPort = "3306"
	}

	// List all databases
	listCmd := exec.Command("mysql",
		"-h", mysqlHost,
		"-P", mysqlPort,
		"-u", mysqlRootUsername,
		fmt.Sprintf("-p%s", mysqlRootPassword),
		"-e", "SHOW DATABASES")

	output, err := listCmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to list MySQL databases: %v, output: %s", err, string(output))
	}

	// Parse the output to find temporary databases (containing 'tracker')
	databases := strings.Split(string(output), "\n")
	for _, db := range databases {
		db = strings.TrimSpace(db)
		if db != "" && strings.Contains(strings.ToLower(db), "tracker") {
			log.Printf("Dropping MySQL database: %s", db)

			// Drop the database
			dropCmd := exec.Command("mysql",
				"-h", mysqlHost,
				"-P", mysqlPort,
				"-u", mysqlRootUsername,
				fmt.Sprintf("-p%s", mysqlRootPassword),
				"-e", fmt.Sprintf("DROP DATABASE IF EXISTS `%s`", db))

			dropOutput, dropErr := dropCmd.CombinedOutput()
			if dropErr != nil {
				log.Printf("Warning: Failed to drop MySQL database %s: %v, output: %s", db, dropErr, string(dropOutput))
			} else {
				log.Printf("Successfully dropped MySQL database: %s", db)
			}
		}
	}

	return nil
}

// dropPostgreSQLTemporaryDatabases drops all temporary databases on PostgreSQL pods
func dropPostgreSQLTemporaryDatabases() error {
	log.Printf("Dropping temporary PostgreSQL databases")

	// Get PostgreSQL root credentials from environment variables
	pgRootUsername := os.Getenv("POSTGRES_ROOT_USERNAME")
	if pgRootUsername == "" {
		pgRootUsername = "postgres"
	}

	pgRootPassword := os.Getenv("POSTGRES_ROOT_PASSWORD")
	if pgRootPassword == "" {
		log.Printf("Warning: POSTGRES_ROOT_PASSWORD environment variable not set")
		return fmt.Errorf("POSTGRES_ROOT_PASSWORD environment variable not set")
	}

	// Get PostgreSQL host from environment variable
	pgHost := os.Getenv("POSTGRES_HOST")
	if pgHost == "" {
		pgHost = "postgres"
	}

	// Get PostgreSQL port from environment variable
	pgPort := os.Getenv("POSTGRES_PORT")
	if pgPort == "" {
		pgPort = "5432"
	}

	// List all databases
	listCmd := exec.Command("psql",
		"-h", pgHost,
		"-p", pgPort,
		"-U", pgRootUsername,
		"-d", "postgres",
		"-t",
		"-c", "SELECT datname FROM pg_database")
	listCmd.Env = append(os.Environ(), fmt.Sprintf("PGPASSWORD=%s", pgRootPassword))

	output, err := listCmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to list PostgreSQL databases: %v, output: %s", err, string(output))
	}

	// Parse the output to find temporary databases (containing 'ares')
	databases := strings.Split(string(output), "\n")
	for _, db := range databases {
		db = strings.TrimSpace(db)
		if db != "" && strings.Contains(strings.ToLower(db), "ares") {
			log.Printf("Dropping PostgreSQL database: %s", db)

			// Drop the database
			dropCmd := exec.Command("psql",
				"-h", pgHost,
				"-p", pgPort,
				"-U", pgRootUsername,
				"-d", "postgres",
				"-c", fmt.Sprintf("DROP DATABASE IF EXISTS \"%s\"", db))
			dropCmd.Env = append(os.Environ(), fmt.Sprintf("PGPASSWORD=%s", pgRootPassword))

			dropOutput, dropErr := dropCmd.CombinedOutput()
			if dropErr != nil {
				log.Printf("Warning: Failed to drop PostgreSQL database %s: %v, output: %s", db, dropErr, string(dropOutput))
			} else {
				log.Printf("Successfully dropped PostgreSQL database: %s", db)
			}
		}
	}

	return nil
}

// DropTemporaryDatabaseByName drops a specific temporary database by name
func DropTemporaryDatabaseByName(dbName string) error {
	log.Printf("Dropping specific temporary database: %s", dbName)

	// Check if the database name contains 'tracker' (MySQL) or 'ares' (PostgreSQL)
	if strings.Contains(strings.ToLower(dbName), "tracker") {
		// This is a MySQL database
		return dropSpecificMySQLDatabase(dbName)
	} else if strings.Contains(strings.ToLower(dbName), "ares") {
		// This is a PostgreSQL database
		return dropSpecificPostgreSQLDatabase(dbName)
	} else {
		return fmt.Errorf("database name %s does not match known patterns (tracker or ares)", dbName)
	}
}

// dropSpecificMySQLDatabase drops a specific MySQL database
func dropSpecificMySQLDatabase(dbName string) error {
	log.Printf("Dropping specific MySQL database: %s", dbName)

	// Get MySQL root credentials from environment variables
	mysqlRootUsername := os.Getenv("MYSQL_ROOT_USERNAME")
	if mysqlRootUsername == "" {
		mysqlRootUsername = "root"
	}

	mysqlRootPassword := os.Getenv("MYSQL_ROOT_PASSWORD")
	if mysqlRootPassword == "" {
		log.Printf("Warning: MYSQL_ROOT_PASSWORD environment variable not set")
		return fmt.Errorf("MYSQL_ROOT_PASSWORD environment variable not set")
	}

	// Get MySQL host from environment variable
	mysqlHost := os.Getenv("MYSQL_HOST")
	if mysqlHost == "" {
		mysqlHost = "mysql"
	}

	// Get MySQL port from environment variable
	mysqlPort := os.Getenv("MYSQL_PORT")
	if mysqlPort == "" {
		mysqlPort = "3306"
	}

	// Drop the database
	dropCmd := exec.Command("mysql",
		"-h", mysqlHost,
		"-P", mysqlPort,
		"-u", mysqlRootUsername,
		fmt.Sprintf("-p%s", mysqlRootPassword),
		"-e", fmt.Sprintf("DROP DATABASE IF EXISTS `%s`", dbName))

	dropOutput, dropErr := dropCmd.CombinedOutput()
	if dropErr != nil {
		return fmt.Errorf("failed to drop MySQL database %s: %v, output: %s", dbName, dropErr, string(dropOutput))
	}

	log.Printf("Successfully dropped MySQL database: %s", dbName)
	return nil
}

// dropSpecificPostgreSQLDatabase drops a specific PostgreSQL database
func dropSpecificPostgreSQLDatabase(dbName string) error {
	log.Printf("Dropping specific PostgreSQL database: %s", dbName)

	// Get PostgreSQL root credentials from environment variables
	pgRootUsername := os.Getenv("POSTGRES_ROOT_USERNAME")
	if pgRootUsername == "" {
		pgRootUsername = "postgres"
	}

	pgRootPassword := os.Getenv("POSTGRES_ROOT_PASSWORD")
	if pgRootPassword == "" {
		log.Printf("Warning: POSTGRES_ROOT_PASSWORD environment variable not set")
		return fmt.Errorf("POSTGRES_ROOT_PASSWORD environment variable not set")
	}

	// Get PostgreSQL host from environment variable
	pgHost := os.Getenv("POSTGRES_HOST")
	if pgHost == "" {
		pgHost = "postgres"
	}

	// Get PostgreSQL port from environment variable
	pgPort := os.Getenv("POSTGRES_PORT")
	if pgPort == "" {
		pgPort = "5432"
	}

	// Drop the database
	dropCmd := exec.Command("psql",
		"-h", pgHost,
		"-p", pgPort,
		"-U", pgRootUsername,
		"-d", "postgres",
		"-c", fmt.Sprintf("DROP DATABASE IF EXISTS \"%s\"", dbName))
	dropCmd.Env = append(os.Environ(), fmt.Sprintf("PGPASSWORD=%s", pgRootPassword))

	dropOutput, dropErr := dropCmd.CombinedOutput()
	if dropErr != nil {
		return fmt.Errorf("failed to drop PostgreSQL database %s: %v, output: %s", dbName, dropErr, string(dropOutput))
	}

	log.Printf("Successfully dropped PostgreSQL database: %s", dbName)
	return nil
}

// CreateSanitizeJobGeneric creates a new job to sanitize a database (generic version)
func CreateSanitizeJobGeneric(clientset *kubernetes.Clientset, dbConfig interface{}) (*batchv1.Job, error) {
	// Implementation depends on the specific requirements
	// This is a placeholder for the existing implementation
	return nil, fmt.Errorf("not implemented")
}
