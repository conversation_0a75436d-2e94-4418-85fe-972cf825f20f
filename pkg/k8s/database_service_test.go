package k8s

import (
	"reflect"
	"testing"
)

func TestAnalyzeRequiredDatabaseTypes(t *testing.T) {
	tests := []struct {
		name     string
		configs  []interface{}
		expected []DatabaseType
	}{
		{
			name: "MySQL and PostgreSQL enabled",
			configs: []interface{}{
				map[string]interface{}{
					"type":    "mysql",
					"enabled": true,
				},
				map[string]interface{}{
					"type":    "postgresql",
					"enabled": true,
				},
			},
			expected: []DatabaseType{MySQL, PostgreSQL},
		},
		{
			name: "Only MySQL enabled",
			configs: []interface{}{
				map[string]interface{}{
					"type":    "mysql",
					"enabled": true,
				},
				map[string]interface{}{
					"type":    "postgresql",
					"enabled": false,
				},
			},
			expected: []DatabaseType{MySQL},
		},
		{
			name: "Only PostgreSQL enabled",
			configs: []interface{}{
				map[string]interface{}{
					"type":    "postgresql",
					"enabled": true,
				},
				map[string]interface{}{
					"type":    "mysql",
					"enabled": false,
				},
			},
			expected: []DatabaseType{PostgreSQL},
		},
		{
			name: "No enabled databases",
			configs: []interface{}{
				map[string]interface{}{
					"type":    "mysql",
					"enabled": false,
				},
				map[string]interface{}{
					"type":    "postgresql",
					"enabled": false,
				},
			},
			expected: []DatabaseType{},
		},
		{
			name: "Enabled field missing (defaults to enabled)",
			configs: []interface{}{
				map[string]interface{}{
					"type": "mysql",
				},
				map[string]interface{}{
					"type": "postgresql",
				},
			},
			expected: []DatabaseType{MySQL, PostgreSQL},
		},
		{
			name: "Duplicate database types",
			configs: []interface{}{
				map[string]interface{}{
					"type":    "mysql",
					"enabled": true,
				},
				map[string]interface{}{
					"type":    "mysql",
					"enabled": true,
				},
			},
			expected: []DatabaseType{MySQL},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := AnalyzeRequiredDatabaseTypes(tt.configs)

			// Handle nil vs empty slice comparison
			if len(result) == 0 && len(tt.expected) == 0 {
				return // Both are empty, test passes
			}

			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("AnalyzeRequiredDatabaseTypes() = %v, expected %v", result, tt.expected)
			}
		})
	}
}
