name: CICD Workflow

on:
  pull_request:
    branches:
      - main
    types:
      - closed
  release:
    types: [published]
  workflow_dispatch:

env:
  PROJECT_NAME: mneme
  RUN_AND_ATTEMPT_NUMBER: ${{ github.run_number }}_${{ github.run_attempt }}

jobs:
  Build:
    runs-on: [self-hosted, ARM64]
    # These permissions are needed to interact with GitHub's OIDC Token endpoint for aws-actions/configure-aws-credential
    permissions:
      id-token: write
      contents: read

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.21'

      - name: Generate go.sum
        run: go mod tidy

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume: arn:aws:iam::586518255992:role/AmazonECR_Github_CICD_Role
          aws-region: ca-central-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build mneme
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          TAG: 586518255992.dkr.ecr.ca-central-1.amazonaws.com/mneme:${{ github.run_number }}          
        run: >
          docker build
          --no-cache
          --tag $TAG
          -f Dockerfile
          . &&
          docker push $TAG

  Deploy:
    name: Deploy to Production Cluster
    if: github.event_name == 'workflow_dispatch' || (github.event_name == 'pull_request' && github.event.pull_request.merged == true)
    needs: [Build]
    runs-on: ubuntu-latest
    permissions:
      packages: write
      id-token: write
      contents: write
    environment:
      name: production
    env:
      ENVIRONMENT: production
      MNEME_IMAGE: 586518255992.dkr.ecr.ca-central-1.amazonaws.com/mneme:${{ github.run_number }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Kubectl tool installer
        uses: azure/setup-kubectl@v3
        with:
          version: "v1.23.6"
        id: install

      - name: Update Kubernetes namespace & Add instance specific environment variables
        run: |        
          cd k8s/overlays/production
          kustomize edit set namespace mneme-production

      - name: Update images with latest build
        run: |
          cd k8s/overlays/production
          kustomize edit set image mneme=586518255992.dkr.ecr.ca-central-1.amazonaws.com/mneme:${{ github.run_number }}
          echo "Final manifests after kustomize:"
          kustomize build .

      - name: Set the Kubernetes context and deploy the kube-manifests
        run: |
          echo ${{ secrets.KUBECONFIG_PRODUCTION }} | base64 --decode > /tmp/kubeconfig
          export KUBECONFIG=/tmp/kubeconfig
          cd k8s/overlays/production
          
          # Apply namespace first
          kubectl apply -f namespace.yaml
          
          # Apply remaining manifests using kustomize
          kubectl apply -k .
